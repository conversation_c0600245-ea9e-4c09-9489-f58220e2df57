<?php
/**
 * SQL Analysis Queries for Bamboo Database
 * Useful queries for database analysis and reporting
 */

require_once 'includes/config.php';
require_once 'includes/database.php';

echo "=== BAMBOO DATABASE ANALYSIS QUERIES ===\n\n";

try {
    $db = getDB();
    
    // 1. User Statistics Overview
    echo "1. USER STATISTICS OVERVIEW\n";
    echo str_repeat("=", 40) . "\n";
    
    $query = "
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_users,
            COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_users,
            COUNT(CASE WHEN status = 'banned' THEN 1 END) as banned_users,
            AVG(balance) as avg_balance,
            SUM(balance) as total_balance,
            AVG(vip_level) as avg_vip_level
        FROM users
    ";
    
    $result = $db->query($query)->fetch();
    foreach ($result as $key => $value) {
        echo sprintf("%-20s: %s\n", ucwords(str_replace('_', ' ', $key)), $value);
    }
    echo "\n";
    
    // 2. VIP Level Distribution
    echo "2. VIP LEVEL DISTRIBUTION\n";
    echo str_repeat("=", 40) . "\n";
    
    $query = "
        SELECT 
            v.level,
            v.name,
            COUNT(u.id) as user_count,
            ROUND(COUNT(u.id) * 100.0 / (SELECT COUNT(*) FROM users), 2) as percentage,
            AVG(u.balance) as avg_balance,
            SUM(u.balance) as total_balance
        FROM vip_levels v
        LEFT JOIN users u ON v.level = u.vip_level
        GROUP BY v.level, v.name
        ORDER BY v.level
    ";
    
    $results = $db->query($query)->fetchAll();
    echo sprintf("%-10s %-15s %-12s %-12s %-15s %s\n", "Level", "Name", "Users", "Percentage", "Avg Balance", "Total Balance");
    echo str_repeat("-", 80) . "\n";
    foreach ($results as $row) {
        echo sprintf("%-10s %-15s %-12s %-12s %-15s %s\n", 
            $row['level'], 
            $row['name'], 
            $row['user_count'], 
            $row['percentage'] . '%',
            number_format($row['avg_balance'], 2),
            number_format($row['total_balance'], 2)
        );
    }
    echo "\n";
    
    // 3. Task Completion Statistics
    echo "3. TASK COMPLETION STATISTICS\n";
    echo str_repeat("=", 40) . "\n";
    
    $query = "
        SELECT 
            status,
            COUNT(*) as task_count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM tasks), 2) as percentage,
            AVG(amount) as avg_amount,
            SUM(commission_earned) as total_commission
        FROM tasks
        GROUP BY status
        ORDER BY task_count DESC
    ";
    
    $results = $db->query($query)->fetchAll();
    echo sprintf("%-15s %-12s %-12s %-15s %s\n", "Status", "Count", "Percentage", "Avg Amount", "Total Commission");
    echo str_repeat("-", 70) . "\n";
    foreach ($results as $row) {
        echo sprintf("%-15s %-12s %-12s %-15s %s\n", 
            $row['status'], 
            $row['task_count'], 
            $row['percentage'] . '%',
            number_format($row['avg_amount'], 2),
            number_format($row['total_commission'], 2)
        );
    }
    echo "\n";
    
    // 4. Transaction Analysis
    echo "4. TRANSACTION ANALYSIS\n";
    echo str_repeat("=", 40) . "\n";
    
    $query = "
        SELECT 
            type,
            COUNT(*) as transaction_count,
            SUM(amount) as total_amount,
            AVG(amount) as avg_amount,
            MIN(amount) as min_amount,
            MAX(amount) as max_amount
        FROM transactions
        GROUP BY type
        ORDER BY total_amount DESC
    ";
    
    $results = $db->query($query)->fetchAll();
    echo sprintf("%-20s %-8s %-15s %-12s %-12s %s\n", "Type", "Count", "Total Amount", "Avg Amount", "Min Amount", "Max Amount");
    echo str_repeat("-", 90) . "\n";
    foreach ($results as $row) {
        echo sprintf("%-20s %-8s %-15s %-12s %-12s %s\n", 
            $row['type'], 
            $row['transaction_count'], 
            number_format($row['total_amount'], 2),
            number_format($row['avg_amount'], 2),
            number_format($row['min_amount'], 2),
            number_format($row['max_amount'], 2)
        );
    }
    echo "\n";
    
    // 5. Top Performing Products
    echo "5. TOP PERFORMING PRODUCTS\n";
    echo str_repeat("=", 40) . "\n";
    
    $query = "
        SELECT 
            p.name,
            p.price,
            p.commission_rate,
            COUNT(t.id) as task_count,
            SUM(t.commission_earned) as total_commission,
            AVG(t.commission_earned) as avg_commission
        FROM products p
        LEFT JOIN tasks t ON p.id = t.product_id
        WHERE p.status = 'active'
        GROUP BY p.id, p.name, p.price, p.commission_rate
        ORDER BY task_count DESC
        LIMIT 10
    ";
    
    $results = $db->query($query)->fetchAll();
    echo sprintf("%-25s %-10s %-8s %-10s %-15s %s\n", "Product Name", "Price", "Comm%", "Tasks", "Total Comm", "Avg Comm");
    echo str_repeat("-", 85) . "\n";
    foreach ($results as $row) {
        echo sprintf("%-25s %-10s %-8s %-10s %-15s %s\n", 
            substr($row['name'], 0, 24), 
            number_format($row['price'], 2),
            $row['commission_rate'] . '%',
            $row['task_count'],
            number_format($row['total_commission'], 2),
            number_format($row['avg_commission'], 2)
        );
    }
    echo "\n";
    
    // 6. User Activity Analysis
    echo "6. USER ACTIVITY ANALYSIS (Last 30 Days)\n";
    echo str_repeat("=", 40) . "\n";
    
    $query = "
        SELECT 
            u.username,
            u.vip_level,
            u.balance,
            COUNT(t.id) as tasks_completed,
            SUM(t.commission_earned) as total_commission,
            u.last_login
        FROM users u
        LEFT JOIN tasks t ON u.id = t.user_id AND t.status = 'completed' 
            AND t.completed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        WHERE u.status = 'active'
        GROUP BY u.id, u.username, u.vip_level, u.balance, u.last_login
        ORDER BY tasks_completed DESC, total_commission DESC
        LIMIT 10
    ";
    
    $results = $db->query($query)->fetchAll();
    echo sprintf("%-15s %-8s %-12s %-8s %-15s %s\n", "Username", "VIP", "Balance", "Tasks", "Commission", "Last Login");
    echo str_repeat("-", 75) . "\n";
    foreach ($results as $row) {
        echo sprintf("%-15s %-8s %-12s %-8s %-15s %s\n", 
            $row['username'], 
            $row['vip_level'],
            number_format($row['balance'], 2),
            $row['tasks_completed'],
            number_format($row['total_commission'], 2),
            $row['last_login'] ? date('Y-m-d H:i', strtotime($row['last_login'])) : 'Never'
        );
    }
    echo "\n";
    
    // 7. Financial Summary
    echo "7. FINANCIAL SUMMARY\n";
    echo str_repeat("=", 40) . "\n";
    
    $query = "
        SELECT 
            'Total User Balances' as metric,
            SUM(balance) as amount
        FROM users
        UNION ALL
        SELECT 
            'Total Commission Balances' as metric,
            SUM(commission_balance) as amount
        FROM users
        UNION ALL
        SELECT 
            'Total Deposits' as metric,
            SUM(amount) as amount
        FROM transactions 
        WHERE type = 'deposit' AND status = 'completed'
        UNION ALL
        SELECT 
            'Total Withdrawals' as metric,
            SUM(amount) as amount
        FROM transactions 
        WHERE type = 'withdrawal' AND status = 'completed'
        UNION ALL
        SELECT 
            'Total Commissions Paid' as metric,
            SUM(amount) as amount
        FROM transactions 
        WHERE type = 'commission' AND status = 'completed'
    ";
    
    $results = $db->query($query)->fetchAll();
    foreach ($results as $row) {
        echo sprintf("%-25s: $%s\n", $row['metric'], number_format($row['amount'], 2));
    }
    echo "\n";
    
    // 8. Referral System Analysis
    echo "8. REFERRAL SYSTEM ANALYSIS\n";
    echo str_repeat("=", 40) . "\n";
    
    $query = "
        SELECT 
            s.name as superior_name,
            s.invitation_code,
            s.invited_count,
            COUNT(u.id) as actual_referrals,
            AVG(u.balance) as avg_referral_balance,
            SUM(u.balance) as total_referral_balance
        FROM superiors s
        LEFT JOIN users u ON s.invitation_code = u.invited_by
        GROUP BY s.id, s.name, s.invitation_code, s.invited_count
        ORDER BY actual_referrals DESC
    ";
    
    $results = $db->query($query)->fetchAll();
    echo sprintf("%-15s %-12s %-8s %-8s %-15s %s\n", "Superior", "Code", "Claimed", "Actual", "Avg Balance", "Total Balance");
    echo str_repeat("-", 75) . "\n";
    foreach ($results as $row) {
        echo sprintf("%-15s %-12s %-8s %-8s %-15s %s\n", 
            $row['superior_name'], 
            $row['invitation_code'],
            $row['invited_count'],
            $row['actual_referrals'],
            number_format($row['avg_referral_balance'], 2),
            number_format($row['total_referral_balance'], 2)
        );
    }
    echo "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "=== ANALYSIS COMPLETE ===\n";
?>