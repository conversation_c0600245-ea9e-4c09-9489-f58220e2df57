<?php
/**
 * Bamboo User Application - Profile Edit Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: User profile editing functionality
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        $username = trim($_POST['username'] ?? '');
        $gender = trim($_POST['gender'] ?? '');
        $current_password = trim($_POST['current_password'] ?? '');
        $new_password = trim($_POST['new_password'] ?? '');
        $confirm_password = trim($_POST['confirm_password'] ?? '');
        $withdrawal_pin = trim($_POST['withdrawal_pin'] ?? '');
        $confirm_withdrawal_pin = trim($_POST['confirm_withdrawal_pin'] ?? '');

        // Validation
        $errors = [];
        
        // Username validation
        if (empty($username)) {
            $errors[] = 'Username is required.';
        } elseif ($username !== $current_user['username']) {
            // Check if username is already taken
            $existing_user = fetchRow("SELECT id FROM users WHERE username = ? AND id != ?", [$username, $user_id]);
            if ($existing_user) {
                $errors[] = 'Username is already taken.';
            }
        }
        
        // Gender validation
        if (empty($gender) || !in_array($gender, ['male', 'female'])) {
            $errors[] = 'Please select a valid gender.';
        }
        
        // Password validation (only if changing password)
        if (!empty($new_password) || !empty($confirm_password)) {
            if (empty($current_password)) {
                $errors[] = 'Current password is required to change password.';
            } elseif (!password_verify($current_password, $current_user['password_hash'])) {
                $errors[] = 'Current password is incorrect.';
            } elseif (strlen($new_password) < 6) {
                $errors[] = 'New password must be at least 6 characters long.';
            } elseif ($new_password !== $confirm_password) {
                $errors[] = 'New password and confirmation do not match.';
            }
        }
        
        // Withdrawal PIN validation (only if changing PIN)
        if (!empty($withdrawal_pin) || !empty($confirm_withdrawal_pin)) {
            $current_withdrawal_pin = trim($_POST['current_withdrawal_pin'] ?? '');

            // Check if user has existing PIN
            if (!empty($current_user['withdrawal_pin_hash'])) {
                if (empty($current_withdrawal_pin)) {
                    $errors[] = 'Current withdrawal PIN is required to change PIN.';
                } elseif (!password_verify($current_withdrawal_pin, $current_user['withdrawal_pin_hash'])) {
                    $errors[] = 'Current withdrawal PIN is incorrect.';
                }
            }

            if (strlen($withdrawal_pin) < 4) {
                $errors[] = 'New withdrawal PIN must be at least 4 characters long.';
            } elseif ($withdrawal_pin !== $confirm_withdrawal_pin) {
                $errors[] = 'New withdrawal PIN and confirmation do not match.';
            }
        }
        
        if (empty($errors)) {
            try {
                // Prepare update data
                $update_data = [
                    'username' => $username,
                    'gender' => $gender
                ];

                // Handle avatar upload
                if (isset($_FILES['avatar_upload']) && $_FILES['avatar_upload']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = '../../../uploads/avatars/';
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }

                    $file_info = pathinfo($_FILES['avatar_upload']['name']);
                    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                    $file_extension = strtolower($file_info['extension']);

                    if (in_array($file_extension, $allowed_extensions)) {
                        $new_filename = 'avatar_' . $user_id . '_' . time() . '.' . $file_extension;
                        $upload_path = $upload_dir . $new_filename;

                        if (move_uploaded_file($_FILES['avatar_upload']['tmp_name'], $upload_path)) {
                            // Delete old avatar if exists
                            if (!empty($current_user['avatar']) && file_exists($upload_dir . $current_user['avatar'])) {
                                unlink($upload_dir . $current_user['avatar']);
                            }
                            $update_data['avatar'] = $new_filename;
                        }
                    }
                }

                // Add password if changing
                if (!empty($new_password)) {
                    $update_data['password_hash'] = password_hash($new_password, PASSWORD_DEFAULT);
                }

                // Add withdrawal PIN if changing
                if (!empty($withdrawal_pin)) {
                    $update_data['withdrawal_pin_hash'] = password_hash($withdrawal_pin, PASSWORD_DEFAULT);
                }
                
                // Update user record
                if (updateRecord('users', $update_data, 'id = ?', [$user_id])) {
                    showSuccess('Profile updated successfully!');
                    
                    // Refresh current user data
                    $current_user = getCurrentUser();
                } else {
                    showError('Failed to update profile. Please try again.');
                }
            } catch (Exception $e) {
                error_log("Profile update error: " . $e->getMessage());
                showError('An error occurred while updating your profile. Please try again.');
            }
        } else {
            foreach ($errors as $error) {
                showError($error);
            }
        }
    }
}

// Set page title and description
$page_title = 'Edit Profile';
$page_description = 'Update your profile information';

// Override CSS and JS paths for this nested directory
$additional_css = [BASE_URL . 'user/profile/edit/edit-profile.css'];
$additional_js = [BASE_URL . 'user/profile/edit/edit-profile.js'];

// Include header
include '../../includes/user_header.php';
?>

<div class="user-container">
    <!-- Edit Profile Page Title -->
    <div class="edit-profile-page-title user-fade-in">
        <div class="user-container">
            <h1 class="page-title">✏️ Edit Profile</h1>
            <p class="page-subtitle">Update your profile information and security settings</p>
        </div>
    </div>

    <!-- Profile Edit Form -->
    <div class="user-card edit-profile-form-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Profile Information</h5>
            <span class="form-status">
                🔒 Secure
            </span>
        </div>
        <div class="user-card-body">
            <form method="POST" class="edit-profile-form" id="editProfileForm" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <!-- Avatar Section -->
                <div class="form-section">
                    <h6 class="section-title">Profile Avatar</h6>
                    <div class="avatar-management">
                        <div class="current-avatar">
                            <?php if (!empty($current_user['avatar'])): ?>
                                <img src="<?php echo BASE_URL . 'uploads/avatars/' . $current_user['avatar']; ?>"
                                     alt="Current Avatar"
                                     class="avatar-preview"
                                     id="avatarPreview">
                            <?php else: ?>
                                <div class="avatar-preview-initials" id="avatarPreview">
                                    <?php echo strtoupper(substr($current_user['username'], 0, 2)); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="avatar-actions">
                            <input type="file"
                                   id="avatar_upload"
                                   name="avatar_upload"
                                   accept="image/*"
                                   class="avatar-input"
                                   style="display: none;">
                            <button type="button"
                                    class="user-btn user-btn-outline"
                                    onclick="document.getElementById('avatar_upload').click()">
                                📷 Change Avatar
                            </button>
                            <small class="form-help">JPG, PNG, GIF up to 2MB</small>
                        </div>
                    </div>
                </div>

                <!-- Basic Information -->
                <div class="form-section">
                    <h6 class="section-title">Basic Information</h6>
                    
                    <!-- Username -->
                    <div class="form-group">
                        <label for="username" class="form-label">
                            <span class="label-text">Username</span>
                            <span class="required">*</span>
                        </label>
                        <input type="text"
                               id="username"
                               name="username"
                               class="form-control"
                               value="<?php echo htmlspecialchars($current_user['username']); ?>"
                               required
                               minlength="3"
                               maxlength="50">
                        <small class="form-help">Your unique username for login</small>
                    </div>

                    <!-- Gender -->
                    <div class="form-group">
                        <label for="gender" class="form-label">
                            <span class="label-text">Gender</span>
                            <span class="required">*</span>
                        </label>
                        <select id="gender" name="gender" class="form-control" required>
                            <option value="">Select Gender</option>
                            <option value="male" <?php echo ($current_user['gender'] === 'male') ? 'selected' : ''; ?>>Male</option>
                            <option value="female" <?php echo ($current_user['gender'] === 'female') ? 'selected' : ''; ?>>Female</option>
                        </select>
                        <small class="form-help">Select your gender</small>
                    </div>
                </div>

                <!-- Password Section -->
                <div class="form-section">
                    <h6 class="section-title">Change Password</h6>
                    <p class="section-description">Leave blank if you don't want to change your password</p>
                    
                    <!-- Current Password -->
                    <div class="form-group">
                        <label for="current_password" class="form-label">
                            <span class="label-text">Current Password</span>
                        </label>
                        <input type="password"
                               id="current_password"
                               name="current_password"
                               class="form-control"
                               placeholder="Enter current password">
                        <small class="form-help">Required only if changing password</small>
                    </div>

                    <!-- New Password -->
                    <div class="form-group">
                        <label for="new_password" class="form-label">
                            <span class="label-text">New Password</span>
                        </label>
                        <input type="password"
                               id="new_password"
                               name="new_password"
                               class="form-control"
                               placeholder="Enter new password"
                               minlength="6">
                        <small class="form-help">Minimum 6 characters</small>
                    </div>

                    <!-- Confirm New Password -->
                    <div class="form-group">
                        <label for="confirm_password" class="form-label">
                            <span class="label-text">Confirm New Password</span>
                        </label>
                        <input type="password"
                               id="confirm_password"
                               name="confirm_password"
                               class="form-control"
                               placeholder="Confirm new password">
                        <small class="form-help">Must match new password</small>
                    </div>
                </div>

                <!-- Withdrawal PIN Section -->
                <div class="form-section">
                    <h6 class="section-title">Withdrawal PIN</h6>
                    <p class="section-description">
                        <?php if (!empty($current_user['withdrawal_pin_hash'])): ?>
                            Your withdrawal PIN is currently set. Leave blank if you don't want to change it.
                        <?php else: ?>
                            Set a withdrawal PIN for secure withdrawals.
                        <?php endif; ?>
                    </p>
                    
                    <!-- Current Withdrawal PIN (only show if PIN exists) -->
                    <?php if (!empty($current_user['withdrawal_pin_hash'])): ?>
                    <div class="form-group">
                        <label for="current_withdrawal_pin" class="form-label">
                            <span class="label-text">Current Withdrawal PIN</span>
                        </label>
                        <input type="password"
                               id="current_withdrawal_pin"
                               name="current_withdrawal_pin"
                               class="form-control"
                               placeholder="Enter current withdrawal PIN">
                        <small class="form-help">Required only if changing withdrawal PIN</small>
                    </div>
                    <?php endif; ?>

                    <!-- New Withdrawal PIN -->
                    <div class="form-group">
                        <label for="withdrawal_pin" class="form-label">
                            <span class="label-text"><?php echo !empty($current_user['withdrawal_pin_hash']) ? 'New Withdrawal PIN' : 'Withdrawal PIN'; ?></span>
                        </label>
                        <input type="password"
                               id="withdrawal_pin"
                               name="withdrawal_pin"
                               class="form-control"
                               placeholder="<?php echo !empty($current_user['withdrawal_pin_hash']) ? 'Enter new withdrawal PIN' : 'Enter withdrawal PIN'; ?>"
                               minlength="4">
                        <small class="form-help">Minimum 4 characters (numbers or letters)</small>
                    </div>

                    <!-- Confirm Withdrawal PIN -->
                    <div class="form-group">
                        <label for="confirm_withdrawal_pin" class="form-label">
                            <span class="label-text">Confirm Withdrawal PIN</span>
                        </label>
                        <input type="password"
                               id="confirm_withdrawal_pin"
                               name="confirm_withdrawal_pin"
                               class="form-control"
                               placeholder="Confirm withdrawal PIN">
                        <small class="form-help">Must match withdrawal PIN</small>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="form-actions">
                    <button type="submit" class="user-btn user-btn-primary user-btn-lg">
                        💾 Update Profile
                    </button>
                </div>
            </form>

            <!-- Back to Profile Link -->
            <div class="back-link">
                <a href="<?php echo BASE_URL; ?>user/profile/" class="user-btn user-btn-outline">
                    ← Back to Profile
                </a>
            </div>
        </div>
    </div>
</div>

<?php include '../../includes/user_footer.php'; ?>
