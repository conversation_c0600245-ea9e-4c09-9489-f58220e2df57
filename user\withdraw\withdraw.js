/**
 * Bamboo User Application - Withdraw Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

// Withdraw page utilities
const WithdrawUtils = {
    // Initialize the withdraw page
    init: function() {
        this.setupFormValidation();
        this.setupAmountFormatting();
        this.setupPINValidation();
        console.log('Withdraw page initialized successfully');
    },

    // Setup form validation
    setupFormValidation: function() {
        const form = document.getElementById('withdrawForm');
        if (!form) return;

        // Real-time validation
        const amountInput = document.getElementById('amount');
        const pinInput = document.getElementById('withdrawal_pin');

        if (amountInput) {
            amountInput.addEventListener('input', this.validateAmount.bind(this));
            amountInput.addEventListener('blur', this.validateAmount.bind(this));
        }

        if (pinInput) {
            pinInput.addEventListener('input', this.validatePIN.bind(this));
            pinInput.addEventListener('blur', this.validatePIN.bind(this));
        }

        // Form submission validation
        form.addEventListener('submit', this.validateForm.bind(this));
    },

    // Setup amount input formatting
    setupAmountFormatting: function() {
        const amountInput = document.getElementById('amount');
        if (!amountInput) return;

        amountInput.addEventListener('input', function(e) {
            let value = e.target.value;
            
            // Remove any non-numeric characters except decimal point
            value = value.replace(/[^0-9.]/g, '');
            
            // Ensure only one decimal point
            const parts = value.split('.');
            if (parts.length > 2) {
                value = parts[0] + '.' + parts.slice(1).join('');
            }
            
            // Limit to 2 decimal places
            if (parts[1] && parts[1].length > 2) {
                value = parts[0] + '.' + parts[1].substring(0, 2);
            }
            
            e.target.value = value;
        });

        // Add quick amount buttons
        this.addQuickAmountButtons();
    },

    // Add quick amount selection buttons
    addQuickAmountButtons: function() {
        const amountInput = document.getElementById('amount');
        if (!amountInput) return;

        const maxAmount = parseFloat(amountInput.getAttribute('max')) || 0;
        if (maxAmount <= 0) return;

        const formGroup = amountInput.closest('.form-group');
        if (!formGroup) return;

        const quickAmounts = [
            { label: '25%', value: maxAmount * 0.25 },
            { label: '50%', value: maxAmount * 0.50 },
            { label: '75%', value: maxAmount * 0.75 },
            { label: 'Max', value: maxAmount }
        ];

        const quickButtonsContainer = document.createElement('div');
        quickButtonsContainer.className = 'quick-amounts';
        quickButtonsContainer.innerHTML = '<small class="form-help">Quick amounts:</small>';

        const buttonsWrapper = document.createElement('div');
        buttonsWrapper.className = 'quick-amount-buttons';

        quickAmounts.forEach(amount => {
            if (amount.value >= 0.01) {
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'quick-amount-btn';
                button.textContent = amount.label;
                button.addEventListener('click', () => {
                    amountInput.value = amount.value.toFixed(2);
                    this.validateAmount({ target: amountInput });
                });
                buttonsWrapper.appendChild(button);
            }
        });

        quickButtonsContainer.appendChild(buttonsWrapper);
        formGroup.appendChild(quickButtonsContainer);
    },

    // Validate amount input
    validateAmount: function(event) {
        const field = event.target;
        const value = parseFloat(field.value);
        const maxAmount = parseFloat(field.getAttribute('max')) || 0;
        
        let isValid = true;
        let message = '';

        if (isNaN(value) || value <= 0) {
            isValid = false;
            message = 'Please enter a valid amount';
        } else if (value > maxAmount) {
            isValid = false;
            message = `Amount cannot exceed $${maxAmount.toFixed(2)}`;
        } else if (value < 0.01) {
            isValid = false;
            message = 'Minimum withdrawal amount is $0.01';
        }

        this.setFieldValidation(field, isValid, message);
        return isValid;
    },

    // Validate PIN input
    validatePIN: function(event) {
        const field = event.target;
        const value = field.value.trim();
        
        let isValid = true;
        let message = '';

        if (value.length === 0) {
            isValid = false;
            message = 'Withdrawal PIN is required';
        } else if (value.length < 4) {
            isValid = false;
            message = 'PIN must be at least 4 characters';
        }

        this.setFieldValidation(field, isValid, message);
        return isValid;
    },

    // Set field validation state
    setFieldValidation: function(field, isValid, message) {
        const formGroup = field.closest('.form-group');
        if (!formGroup) return;

        // Remove existing validation classes and messages
        field.classList.remove('is-valid', 'is-invalid');
        const existingFeedback = formGroup.querySelector('.invalid-feedback, .valid-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }

        if (field.value.trim() === '') {
            // No validation styling for empty fields
            return;
        }

        // Add validation classes and messages
        if (isValid) {
            field.classList.add('is-valid');
        } else {
            field.classList.add('is-invalid');
            if (message) {
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = message;
                formGroup.appendChild(feedback);
            }
        }
    },

    // Setup PIN validation
    setupPINValidation: function() {
        const pinInput = document.getElementById('withdrawal_pin');
        if (!pinInput) return;

        // Only allow numeric input for PIN
        pinInput.addEventListener('input', function(e) {
            // Allow any characters for PIN (not just numeric)
            // Some users might have alphanumeric PINs
        });

        // Add show/hide PIN functionality
        this.addPINToggle();
    },

    // Add PIN visibility toggle
    addPINToggle: function() {
        const pinInput = document.getElementById('withdrawal_pin');
        if (!pinInput) return;

        const formGroup = pinInput.closest('.form-group');
        if (!formGroup) return;

        const wrapper = document.createElement('div');
        wrapper.className = 'pin-input-wrapper';
        
        pinInput.parentNode.insertBefore(wrapper, pinInput);
        wrapper.appendChild(pinInput);

        const toggleBtn = document.createElement('button');
        toggleBtn.type = 'button';
        toggleBtn.className = 'pin-toggle-btn';
        toggleBtn.innerHTML = '👁️';
        toggleBtn.title = 'Show/Hide PIN';
        
        toggleBtn.addEventListener('click', function() {
            if (pinInput.type === 'password') {
                pinInput.type = 'text';
                toggleBtn.innerHTML = '🙈';
            } else {
                pinInput.type = 'password';
                toggleBtn.innerHTML = '👁️';
            }
        });

        wrapper.appendChild(toggleBtn);
    },

    // Validate entire form
    validateForm: function(event) {
        const form = event.target;
        const amountInput = document.getElementById('amount');
        const pinInput = document.getElementById('withdrawal_pin');

        let isValid = true;

        // Validate amount
        if (amountInput && !this.validateAmount({ target: amountInput })) {
            isValid = false;
        }

        // Validate PIN
        if (pinInput && !this.validatePIN({ target: pinInput })) {
            isValid = false;
        }

        if (!isValid) {
            event.preventDefault();
            this.showFormError('Please correct the errors above before submitting.');
            return false;
        }

        // Show loading state
        this.showLoadingState(form);
        return true;
    },

    // Show form error
    showFormError: function(message) {
        // Remove existing error messages
        const existingError = document.querySelector('.form-error-message');
        if (existingError) {
            existingError.remove();
        }

        // Create and show error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'form-error-message alert alert-danger';
        errorDiv.textContent = message;

        const form = document.getElementById('withdrawForm');
        if (form) {
            form.insertBefore(errorDiv, form.firstChild);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 5000);
        }
    },

    // Show loading state
    showLoadingState: function(form) {
        form.classList.add('loading');
        
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.textContent = 'Processing...';
        }
    },

    // Format currency display
    formatCurrency: function(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    WithdrawUtils.init();
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WithdrawUtils;
}
