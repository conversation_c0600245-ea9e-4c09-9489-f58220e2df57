<?php
/**
 * Bamboo User Application - Contact Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: User contact page with customer service contacts
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Get customer service contacts
$customer_service_contacts = fetchAll("SELECT * FROM customer_service_contacts WHERE is_active = 1 ORDER BY sort_order ASC");

// Get app settings
$app_name = getAppSetting('app_name', APP_NAME);
$app_logo = getAppSetting('app_logo', '');

// Page configuration
$page_title = 'Contact Us';
$page_description = 'Get in touch with our customer support team';
$page_css = 'contact.css';
$page_js = 'contact.js';

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Hero Section -->
    <div class="contact-hero user-fade-in">
        <div class="hero-content">
            <h1 class="hero-title">Contact Us</h1>
            <p class="hero-description">We're here to help! Get in touch with our customer support team for any questions or assistance.</p>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="contact-info user-fade-in">
        <div class="user-card">
            <div class="user-card-header">
                <h5 class="user-card-title">📞 Customer Support</h5>
                <p class="card-subtitle">Multiple ways to reach our support team</p>
            </div>
            <div class="user-card-body">
                <div class="contact-methods">
                    <div class="contact-method">
                        <div class="method-icon">💬</div>
                        <div class="method-info">
                            <h6>Online Customer Care</h6>
                            <p>Chat with our support team in real-time</p>
                            <button class="user-btn user-btn-primary" id="openCustomerCareBtn">
                                🌐 Open Customer Care
                            </button>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <div class="method-icon">📧</div>
                        <div class="method-info">
                            <h6>Email Support</h6>
                            <p>Send us an email for detailed inquiries</p>
                            <a href="mailto:support@<?php echo strtolower($app_name); ?>.com" class="user-btn user-btn-outline">
                                📧 Send Email
                            </a>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <div class="method-icon">❓</div>
                        <div class="method-info">
                            <h6>FAQ & Help Center</h6>
                            <p>Find answers to common questions</p>
                            <a href="<?php echo BASE_URL; ?>user/faq/" class="user-btn user-btn-outline">
                                📚 View FAQ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Customer Care Modal -->
<div class="modal fade" id="customerCareModal" tabindex="-1" aria-labelledby="customerCareModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerCareModalLabel">
                    <span class="modal-icon">💬</span>
                    Online Customer Care
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="customer-care-intro">
                    <p>Choose a customer service contact method below. You will be redirected to the selected platform to chat with our support team.</p>
                </div>
                
                <div class="customer-care-list">
                    <?php if (!empty($customer_service_contacts)): ?>
                        <?php foreach ($customer_service_contacts as $contact): ?>
                            <div class="care-contact-item" data-link="<?php echo htmlspecialchars($contact['link']); ?>">
                                <div class="contact-icon">
                                    <?php
                                    $icon = '💬';
                                    switch (strtolower($contact['type'] ?? 'other')) {
                                        case 'telegram': $icon = '📱'; break;
                                        case 'whatsapp': $icon = '💚'; break;
                                        case 'email': $icon = '📧'; break;
                                        case 'phone': $icon = '📞'; break;
                                        default: $icon = '💬'; break;
                                    }
                                    echo $icon;
                                    ?>
                                </div>
                                <div class="contact-details">
                                    <h6 class="contact-name"><?php echo htmlspecialchars($contact['name']); ?></h6>
                                    <p class="contact-type"><?php echo ucfirst($contact['type'] ?? 'Support'); ?></p>
                                </div>
                                <div class="contact-action">
                                    <span class="action-icon">→</span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="no-contacts">
                            <div class="no-contacts-icon">📞</div>
                            <h6>No Contact Methods Available</h6>
                            <p>Please check back later or contact us via email.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="user-btn user-btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/user_footer.php'; ?>
