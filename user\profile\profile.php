<?php
/**
 * Bamboo User Application - Profile Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: User profile management page
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Get user balance and statistics
$user_balance = getUserBalance($user_id);
$user_vip = getUserVipLevel($user_id);

// Get user statistics
$user_stats = [
    'total_deposits' => fetchValue("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = ? AND type = 'deposit' AND status = 'completed'", [$user_id]),
    'total_withdrawals' => fetchValue("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = ? AND type = 'withdrawal' AND status = 'completed'", [$user_id]),
    'total_commissions' => fetchValue("SELECT COALESCE(SUM(commission_amount), 0) FROM user_commissions WHERE user_id = ?", [$user_id]),
    'total_tasks' => fetchValue("SELECT COUNT(*) FROM user_tasks WHERE user_id = ? AND status = 'completed'", [$user_id]),
    'referral_count' => fetchValue("SELECT COUNT(*) FROM users WHERE referrer_id = ?", [$user_id])
];

// Get dashboard statistics for Task Overview section
$dashboard_stats = [
    'total_tasks_completed' => getTotalTasksCompleted($user_id),
    'tasks_completed_today' => getTasksCompletedToday($user_id),
    'total_commission_earned' => getTotalCommissionEarned($user_id),
    'referral_count' => getReferralCount($user_id),
    'pending_withdrawals' => getPendingWithdrawals($user_id),
    'active_tasks' => getActiveTasks($user_id),
    'vip_progress' => getVipProgress($user_id)
];

// Get recent transactions (last 4)
$recent_transactions = fetchAll("
    SELECT t.*,
           CASE
               WHEN t.type = 'deposit' THEN 'Deposit'
               WHEN t.type = 'withdrawal' THEN 'Withdraw'
               WHEN t.type = 'commission' THEN 'Commission'
               WHEN t.type = 'task_reward' THEN 'Task Reward'
               WHEN t.type = 'bonus' THEN 'Bonus'
               ELSE UPPER(t.type)
           END as display_type
    FROM transactions t
    WHERE t.user_id = ?
    ORDER BY t.created_at DESC
    LIMIT 4
", [$user_id]);

// Page configuration
$page_title = 'Profile';
$page_description = 'User profile and account management';
$page_css = 'profile.css?v=' . time();
$page_js = 'profile.js';

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Profile Header -->
    <div class="profile-header user-fade-in">
        <div class="profile-avatar-section">
            <div class="profile-avatar">
                <?php if (!empty($current_user['avatar'])): ?>
                    <img src="<?php echo BASE_URL . 'uploads/avatars/' . htmlspecialchars($current_user['avatar']); ?>" alt="Profile Avatar" class="avatar-img">
                <?php else: ?>
                    <div class="avatar-placeholder">
                        <?php echo strtoupper(substr($current_user['username'], 0, 2)); ?>
                    </div>
                <?php endif; ?>
            </div>
            <div class="profile-info">
                <h1 class="profile-username"><?php echo htmlspecialchars(ucfirst($current_user['username'])); ?></h1>
                <div class="profile-level">
                    <span class="vip-badge">VIP <?php echo $user_vip['level'] ?? 1; ?></span>
                </div>
                <div class="profile-stats">
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $dashboard_stats['total_tasks_completed']; ?></span>
                        <span class="stat-label">Tasks Completed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $current_user['credit_score'] ?? 750; ?></span>
                        <span class="stat-label">Credit Score</span>
                    </div>
                </div>


            </div>
        </div>
        
        <div class="profile-balance-section">
            <div class="balance-card">
                <h3>Total Balance</h3>
                <div class="balance-amount">USDT <?php echo formatCurrency($user_balance['balance'] ?? 0); ?></div>
            </div>
            <div class="balance-card">
                <h3>Total Commission Earned</h3>
                <div class="balance-amount">USDT <?php echo formatCurrency($dashboard_stats['total_commission_earned']); ?></div>
            </div>
            <div class="balance-card referral-card">
                <h3>Your Referral Code</h3>
                <div class="referral-code-display">
                    <span id="referralCode"><?php echo htmlspecialchars($current_user['referral_code'] ?? 'N/A'); ?></span>
                    <button class="copy-btn" onclick="copyReferralCode()" title="Copy referral code">
                        📋
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Menu Grid -->
    <div class="profile-menu-grid user-fade-in">
        <!-- Transactions Section -->
        <div class="profile-menu-section">
            <h4 class="section-title">Transactions</h4>
            <div class="menu-items">
                <a href="<?php echo BASE_URL; ?>user/deposit/" class="profile-menu-item">
                    <div class="menu-icon deposit-icon">
                        <i class="icon-deposit"></i>
                    </div>
                    <span class="menu-text">Deposit</span>
                </a>
                
                <a href="<?php echo BASE_URL; ?>user/salary/" class="profile-menu-item">
                    <div class="menu-icon salary-icon">
                        <i class="icon-salary"></i>
                    </div>
                    <span class="menu-text">Salary</span>
                </a>
                
                <a href="<?php echo BASE_URL; ?>user/withdraw/" class="profile-menu-item">
                    <div class="menu-icon withdraw-icon">
                        <i class="icon-withdraw"></i>
                    </div>
                    <span class="menu-text">Withdraw</span>
                </a>
                
                <a href="<?php echo BASE_URL; ?>user/records/" class="profile-menu-item">
                    <div class="menu-icon records-icon">
                        <i class="icon-records"></i>
                    </div>
                    <span class="menu-text">Records</span>
                </a>
            </div>
        </div>

        <!-- Account Management Section -->
        <div class="profile-menu-section">
            <h4 class="section-title">Account Management</h4>
            <div class="menu-items">
                <a href="<?php echo BASE_URL; ?>user/profile/personal-information/" class="profile-menu-item">
                    <div class="menu-icon edit-icon">
                        <i class="icon-edit"></i>
                    </div>
                    <span class="menu-text">Personal Information</span>
                </a>
                
                <a href="<?php echo BASE_URL; ?>user/profile/withdrawal-info/" class="profile-menu-item">
                    <div class="menu-icon withdrawal-info-icon">
                        <i class="icon-bank"></i>
                    </div>
                    <span class="menu-text">Withdrawal Information</span>
                </a>
                
                <a href="<?php echo BASE_URL; ?>user/contact/" class="profile-menu-item">
                    <div class="menu-icon contact-icon">
                        <i class="icon-contact"></i>
                    </div>
                    <span class="menu-text">Contact Us</span>
                </a>
                
                <a href="<?php echo BASE_URL; ?>user/notifications/" class="profile-menu-item">
                    <div class="menu-icon notifications-icon">
                        <i class="icon-notifications"></i>
                    </div>
                    <span class="menu-text">Notifications</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Task Overview -->
    <div class="user-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Task Overview</h5>
            <a href="<?php echo BASE_URL; ?>user/tasks/" class="user-btn user-btn-sm user-btn-outline">View All</a>
        </div>
        <div class="user-card-body">
            <div class="task-stats">
                <div class="task-stat-item">
                    <div class="stat-number"><?php echo $dashboard_stats['tasks_completed_today']; ?></div>
                    <div class="stat-label">Today</div>
                </div>
                <div class="task-stat-item">
                    <div class="stat-number"><?php echo $dashboard_stats['total_tasks_completed']; ?></div>
                    <div class="stat-label">Total Completed</div>
                </div>
                <div class="task-stat-item">
                    <div class="stat-number"><?php echo count($dashboard_stats['active_tasks']); ?></div>
                    <div class="stat-label">Active Tasks</div>
                </div>
                <div class="task-stat-item">
                    <div class="stat-number"><?php echo $user_vip['max_daily_tasks'] ?? 5; ?></div>
                    <div class="stat-label">Daily Limit</div>
                </div>
            </div>

            <?php if (!empty($dashboard_stats['active_tasks'])): ?>
                <div class="active-tasks">
                    <h6>Active Tasks</h6>
                    <?php foreach ($dashboard_stats['active_tasks'] as $task): ?>
                        <div class="task-item">
                            <div class="task-product-image">
                                <?php if (!empty($task['image_url'])): ?>
                                    <img src="<?php echo htmlspecialchars($task['image_url']); ?>" alt="<?php echo htmlspecialchars($task['product_name']); ?>" class="product-thumbnail">
                                <?php else: ?>
                                    <div class="product-placeholder">
                                        <i class="icon-product"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="task-info">
                                <div class="task-title"><?php echo htmlspecialchars($task['product_name']); ?></div>
                                <div class="task-amount"><?php echo formatCurrency($task['amount']); ?></div>
                            </div>
                            <div class="task-status">
                                <span class="status-badge status-<?php echo $task['status']; ?>">
                                    <?php echo ucfirst($task['status']); ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-active-tasks">
                    <p>No active tasks. <a href="<?php echo BASE_URL; ?>user/tasks/">Start a new task</a></p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="user-card recent-transactions-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Recent Transactions</h5>
            <a href="<?php echo BASE_URL; ?>user/transactions/" class="user-btn user-btn-sm user-btn-outline">View All</a>
        </div>
        <div class="user-card-body">
            <?php if (!empty($recent_transactions)): ?>
                <div class="transaction-table">
                    <?php foreach ($recent_transactions as $transaction): ?>
                        <div class="transaction-row">
                            <div class="transaction-type">
                                <div class="type-icon">
                                    <i class="icon-<?php echo $transaction['type']; ?>"></i>
                                </div>
                                <div class="type-info">
                                    <span class="type-name"><?php echo $transaction['display_type']; ?></span>
                                    <span class="type-date"><?php echo date('M j, Y H:i', strtotime($transaction['created_at'])); ?></span>
                                </div>
                            </div>
                            <div class="transaction-amount <?php echo ($transaction['amount'] > 0) ? 'positive' : 'negative'; ?>">
                                <?php echo ($transaction['amount'] > 0) ? '+' : ''; ?><?php echo formatCurrency($transaction['amount']); ?>
                            </div>
                            <div class="transaction-status">
                                <span class="status-badge status-<?php echo $transaction['status']; ?>">
                                    <?php echo ucfirst($transaction['status']); ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-transactions">
                    <div class="no-data-icon">
                        <i class="icon-transactions"></i>
                    </div>
                    <p>No transactions found</p>
                    <a href="<?php echo BASE_URL; ?>user/deposit/" class="user-btn user-btn-primary">Make Your First Deposit</a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Profile Actions Section -->
    <div class="profile-actions-section user-fade-in">
        <a href="<?php echo BASE_URL; ?>user/profile/edit/" class="edit-profile-btn">
            <i class="icon-edit"></i>
            <span>Edit Profile</span>
        </a>
        <a href="<?php echo BASE_URL; ?>user/logout/" class="logout-btn" onclick="return confirm('Are you sure you want to logout?')">
            <i class="icon-logout"></i>
            <span>Logout</span>
        </a>
    </div>
</div>

<?php
// Include footer
include '../includes/user_footer.php';
?>
