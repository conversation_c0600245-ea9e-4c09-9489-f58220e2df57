<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

echo "Adding missing columns to users table...\n";

try {
    // Add referral_code column (using invitation_code as referral_code)
    echo "Adding referral_code column...\n";
    executeQuery("ALTER TABLE users ADD COLUMN referral_code VARCHAR(20) DEFAULT NULL");
    echo "✅ referral_code column added\n";
} catch(Exception $e) {
    echo "⚠️ referral_code column might already exist: " . $e->getMessage() . "\n";
}

try {
    // Add credit_score column
    echo "Adding credit_score column...\n";
    executeQuery("ALTER TABLE users ADD COLUMN credit_score INT DEFAULT 750");
    echo "✅ credit_score column added\n";
} catch(Exception $e) {
    echo "⚠️ credit_score column might already exist: " . $e->getMessage() . "\n";
}

try {
    // Add full_name column
    echo "Adding full_name column...\n";
    executeQuery("ALTER TABLE users ADD COLUMN full_name VARCHAR(100) DEFAULT NULL");
    echo "✅ full_name column added\n";
} catch(Exception $e) {
    echo "⚠️ full_name column might already exist: " . $e->getMessage() . "\n";
}

// Update existing users with referral codes based on invitation_code
echo "Updating referral codes for existing users...\n";
try {
    executeQuery("UPDATE users SET referral_code = invitation_code WHERE referral_code IS NULL AND invitation_code IS NOT NULL");
    echo "✅ Referral codes updated\n";
} catch(Exception $e) {
    echo "⚠️ Error updating referral codes: " . $e->getMessage() . "\n";
}

// Generate referral codes for users who don't have invitation_code
echo "Generating referral codes for users without codes...\n";
try {
    $users_without_codes = fetchAll("SELECT id FROM users WHERE referral_code IS NULL OR referral_code = ''");
    foreach($users_without_codes as $user) {
        $referral_code = 'REF' . strtoupper(substr(md5($user['id'] . time()), 0, 8));
        executeQuery("UPDATE users SET referral_code = ? WHERE id = ?", [$referral_code, $user['id']]);
    }
    echo "✅ Generated referral codes for " . count($users_without_codes) . " users\n";
} catch(Exception $e) {
    echo "⚠️ Error generating referral codes: " . $e->getMessage() . "\n";
}

echo "\n=== UPDATED USERS TABLE STRUCTURE ===\n";
try {
    $result = executeQuery('DESCRIBE users');
    foreach($result as $row) {
        echo $row['Field'] . ' - ' . $row['Type'] . "\n";
    }
} catch(Exception $e) {
    echo 'Error: ' . $e->getMessage() . "\n";
}

echo "\nDatabase updates completed!\n";
?>