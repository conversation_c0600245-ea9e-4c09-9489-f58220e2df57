/**
 * Bamboo User Application - Withdrawal Information Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize withdrawal info page
    initializeWithdrawalInfo();

    // Initialize modify functionality
    initializeModifyFunctionality();

    // Add form validation
    addFormValidation();

    // Handle form submission
    handleFormSubmission();

    // Add input formatting
    addInputFormatting();
});

function initializeWithdrawalInfo() {
    // Add loading states for form elements
    const formInputs = document.querySelectorAll('.form-control');
    formInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            validateField(this);
        });
    });
    
    // Add smooth animations
    const cards = document.querySelectorAll('.user-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
}

function addFormValidation() {
    const form = document.querySelector('.withdrawal-form');
    if (!form) return;
    
    // Real-time validation
    const trc20Input = document.getElementById('trc20_address');
    const phoneInput = document.getElementById('phone');
    
    if (trc20Input) {
        trc20Input.addEventListener('input', function() {
            validateTRC20Address(this);
        });
    }
    
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            validatePhone(this);
        });
    }
}

function validateField(field) {
    switch(field.id) {
        case 'full_name':
            return validateFullName(field);
        case 'trc20_address':
            return validateTRC20Address(field);
        case 'phone':
            return validatePhone(field);
        default:
            return true;
    }
}

function validateFullName(field) {
    const value = field.value.trim();
    const isValid = value.length >= 2 && /^[a-zA-Z\s\-'\.]+$/.test(value);
    
    setFieldValidation(field, isValid, isValid ? '' : 'Please enter a valid full name (letters, spaces, hyphens, and apostrophes only)');
    return isValid;
}

function validateTRC20Address(field) {
    const value = field.value.trim();
    // Basic syntax validation - alphanumeric characters, reasonable length
    const isValid = value.length >= 20 && value.length <= 50 && /^[A-Za-z0-9]+$/.test(value);

    setFieldValidation(field, isValid, isValid ? '' : 'Please enter a valid USDT TRC20 wallet address');
    return isValid;
}

function validatePhone(field) {
    const value = field.value.trim();
    const isValid = /^[\+]?[0-9\s\-\(\)]{10,20}$/.test(value);
    
    setFieldValidation(field, isValid, isValid ? '' : 'Please enter a valid phone number (10-20 digits with optional country code)');
    return isValid;
}

function setFieldValidation(field, isValid, message) {
    const formGroup = field.closest('.form-group');
    const existingError = formGroup.querySelector('.field-error');
    
    // Remove existing error
    if (existingError) {
        existingError.remove();
    }
    
    // Update field styling
    if (isValid) {
        field.classList.remove('invalid');
        field.classList.add('valid');
    } else {
        field.classList.remove('valid');
        field.classList.add('invalid');
        
        // Add error message
        if (message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = message;
            formGroup.appendChild(errorDiv);
        }
    }
}

function addInputFormatting() {
    // Format TRC20 address input
    const trc20Input = document.getElementById('trc20_address');
    if (trc20Input) {
        trc20Input.addEventListener('input', function() {
            // Remove invalid characters, keep alphanumeric only
            let value = this.value.replace(/[^A-Za-z0-9]/g, '');

            // Limit to reasonable length
            if (value.length > 50) {
                value = value.substring(0, 50);
            }

            this.value = value;
        });
    }
    
    // Format phone input
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            // Allow only numbers, spaces, hyphens, parentheses, and plus sign
            let value = this.value.replace(/[^0-9\s\-\(\)\+]/g, '');
            this.value = value;
        });
    }
}

function handleFormSubmission() {
    const form = document.querySelector('.withdrawal-form');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        // Validate all fields before submission
        const fullNameInput = document.getElementById('full_name');
        const trc20Input = document.getElementById('trc20_address');
        const phoneInput = document.getElementById('phone');
        
        let isFormValid = true;
        
        if (fullNameInput && !validateFullName(fullNameInput)) {
            isFormValid = false;
        }
        
        if (trc20Input && !validateTRC20Address(trc20Input)) {
            isFormValid = false;
        }
        
        if (phoneInput && !validatePhone(phoneInput)) {
            isFormValid = false;
        }
        
        if (!isFormValid) {
            e.preventDefault();
            showFormError('Please correct the errors above before submitting.');
            return;
        }
        
        // Add loading state
        addFormLoadingState();
        
        // Show submission feedback
        showSubmissionFeedback();
    });
}

function addFormLoadingState() {
    const form = document.querySelector('.withdrawal-form');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.classList.add('form-loading');
    
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span>💾 Updating...</span>';
    }
}

function showSubmissionFeedback() {
    // Show notification if UserApp is available
    if (typeof UserApp !== 'undefined' && UserApp.showNotification) {
        UserApp.showNotification('Updating withdrawal information...', 'info');
    }
}

function showFormError(message) {
    // Show notification if UserApp is available
    if (typeof UserApp !== 'undefined' && UserApp.showNotification) {
        UserApp.showNotification(message, 'error');
    } else {
        alert(message);
    }
}

// Utility functions
const WithdrawalInfoUtils = {
    // Copy TRC20 address to clipboard
    copyTRC20Address: function() {
        const addressElement = document.querySelector('.trc20-address');
        if (!addressElement) return;
        
        const address = addressElement.textContent.trim();
        if (address === 'Not set') return;
        
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(address).then(function() {
                if (typeof UserApp !== 'undefined' && UserApp.showNotification) {
                    UserApp.showNotification('TRC20 address copied to clipboard!', 'success');
                }
            });
        }
    },
    
    // Validate form before submission
    validateForm: function() {
        const form = document.querySelector('.withdrawal-form');
        if (!form) return false;
        
        const inputs = form.querySelectorAll('.form-control[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    },
    
    // Reset form to original state
    resetForm: function() {
        const form = document.querySelector('.withdrawal-form');
        if (!form) return;
        
        form.classList.remove('form-loading');
        
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '💾 Update Information';
        }
        
        // Clear validation states
        const inputs = form.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.classList.remove('valid', 'invalid');
        });
        
        // Remove error messages
        const errors = form.querySelectorAll('.field-error');
        errors.forEach(error => error.remove());
    }
};

// Add CSS for validation states
const style = document.createElement('style');
style.textContent = `
    .form-control.valid {
        border-color: #10b981;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2310b981' d='m6.564.75-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 0.75rem center;
        background-size: 16px 12px;
    }
    
    .form-control.invalid {
        border-color: #ef4444;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ef4444'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4-2.4 2.4'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 0.75rem center;
        background-size: 16px 12px;
    }
    
    .field-error {
        color: #ef4444;
        font-size: 0.75rem;
        margin-top: 0.25rem;
        display: block;
    }
    
    .form-group.focused .form-label {
        color: #667eea;
    }
`;
document.head.appendChild(style);

// Modify functionality
function initializeModifyFunctionality() {
    const modifyBtn = document.getElementById('modify-btn');
    const cancelBtn = document.getElementById('cancel-btn');
    const infoDisplay = document.getElementById('info-display');
    const editForm = document.getElementById('edit-form');

    if (modifyBtn) {
        modifyBtn.addEventListener('click', function() {
            // Hide info display and show edit form
            infoDisplay.style.display = 'none';
            editForm.style.display = 'block';

            // Focus on first input
            const firstInput = editForm.querySelector('.form-control');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }
        });
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            // Show info display and hide edit form
            editForm.style.display = 'none';
            infoDisplay.style.display = 'block';

            // Reset form to original values
            const form = editForm.querySelector('form');
            if (form) {
                form.reset();
                // Reload the page to get original values
                window.location.reload();
            }
        });
    }
}

// Export for global access
window.WithdrawalInfoUtils = WithdrawalInfoUtils;