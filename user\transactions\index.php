<?php
/**
 * Bamboo User Application - Transactions Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: User transaction history and management page
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Get user balance and VIP info
$user_balance = getUserBalance($user_id);
$user_vip = getUserVipLevel($user_id);

// Get filter parameters
$filter_type = $_GET['type'] ?? 'all';
$filter_status = $_GET['status'] ?? 'all';
$filter_date_from = $_GET['date_from'] ?? '';
$filter_date_to = $_GET['date_to'] ?? '';
$page = intval($_GET['page'] ?? 1);
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build query conditions
$where_conditions = ["user_id = ?"];
$params = [$user_id];

if ($filter_type !== 'all') {
    $where_conditions[] = "type = ?";
    $params[] = $filter_type;
}

if ($filter_status !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_date_from)) {
    $where_conditions[] = "DATE(created_at) >= ?";
    $params[] = $filter_date_from;
}

if (!empty($filter_date_to)) {
    $where_conditions[] = "DATE(created_at) <= ?";
    $params[] = $filter_date_to;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM transactions WHERE $where_clause";
$count_result = fetchRow($count_sql, $params);
$total_transactions = $count_result['total'] ?? 0;
$total_pages = ceil($total_transactions / $per_page);

// Get transactions for current page
$transactions_sql = "
    SELECT t.*, 
           CASE 
               WHEN t.type = 'deposit' THEN 'Deposit'
               WHEN t.type = 'withdrawal' THEN 'Withdrawal'
               WHEN t.type = 'commission' THEN 'Commission'
               WHEN t.type = 'bonus' THEN 'Bonus'
               WHEN t.type = 'referral_bonus' THEN 'Referral Bonus'
               WHEN t.type = 'penalty' THEN 'Penalty'
               WHEN t.type = 'adjustment' THEN 'Adjustment'
               WHEN t.type = 'admin_credit' THEN 'Admin Credit'
               WHEN t.type = 'admin_deduction' THEN 'Admin Deduction'
               ELSE UPPER(t.type)
           END as display_type
    FROM transactions t 
    WHERE $where_clause
    ORDER BY t.created_at DESC 
    LIMIT ? OFFSET ?
";

$params[] = $per_page;
$params[] = $offset;
$transactions = fetchAll($transactions_sql, $params);

// Get transaction summary
$summary_sql = "
    SELECT
        COUNT(*) as total_count,
        SUM(CASE WHEN type = 'deposit' AND status IN ('completed', 'pending') THEN amount ELSE 0 END) as total_deposits,
        SUM(CASE WHEN type = 'withdrawal' AND status IN ('completed', 'pending') THEN amount ELSE 0 END) as total_withdrawals,
        SUM(CASE WHEN type = 'commission' AND status = 'completed' THEN amount ELSE 0 END) as total_commissions,
        SUM(CASE WHEN type = 'bonus' AND status = 'completed' THEN amount ELSE 0 END) as total_bonuses
    FROM transactions
    WHERE user_id = ?
";
$summary = fetchRow($summary_sql, [$user_id]);

// Page configuration
$page_title = 'Transaction History';
$page_description = 'View your complete transaction history and financial activity';
$page_css = 'transactions.css';
$page_js = 'transactions.js';

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Page Header -->
    <div class="transactions-header user-fade-in">
        <div class="header-content">
            <h1 class="page-title">Transaction History</h1>
            <p class="page-subtitle">View and manage your financial transactions</p>
        </div>
        
        <!-- Transaction Summary Cards -->
        <div class="summary-cards">
            <div class="summary-card deposits-card">
                <div class="card-icon">
                    <i class="icon-deposit"></i>
                </div>
                <div class="card-content">
                    <h3>Total Deposits</h3>
                    <div class="amount">USDT <?php echo formatCurrency($summary['total_deposits'] ?? 0); ?></div>
                </div>
            </div>
            
            <div class="summary-card withdrawals-card">
                <div class="card-icon">
                    <i class="icon-withdraw"></i>
                </div>
                <div class="card-content">
                    <h3>Total Withdrawals</h3>
                    <div class="amount">USDT <?php echo formatCurrency($summary['total_withdrawals'] ?? 0); ?></div>
                </div>
            </div>
            
            <div class="summary-card commissions-card">
                <div class="card-icon">
                    <i class="icon-commission"></i>
                </div>
                <div class="card-content">
                    <h3>Total Commissions</h3>
                    <div class="amount">USDT <?php echo formatCurrency($summary['total_commissions'] ?? 0); ?></div>
                </div>
            </div>
            
            <div class="summary-card balance-card">
                <div class="card-icon">
                    <i class="icon-balance"></i>
                </div>
                <div class="card-content">
                    <h3>Current Balance</h3>
                    <div class="amount">USDT <?php echo formatCurrency($user_balance['balance'] ?? 0); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction Filters -->
    <div class="transaction-filters user-fade-in">
        <div class="filter-container">
            <h3 class="filter-title">Filter Transactions</h3>
            
            <form method="GET" class="filter-form">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="type">Transaction Type</label>
                        <select name="type" id="type" class="filter-select">
                            <option value="all" <?php echo $filter_type === 'all' ? 'selected' : ''; ?>>All Types</option>
                            <option value="deposit" <?php echo $filter_type === 'deposit' ? 'selected' : ''; ?>>Deposits</option>
                            <option value="withdrawal" <?php echo $filter_type === 'withdrawal' ? 'selected' : ''; ?>>Withdrawals</option>
                            <option value="commission" <?php echo $filter_type === 'commission' ? 'selected' : ''; ?>>Commissions</option>
                            <option value="bonus" <?php echo $filter_type === 'bonus' ? 'selected' : ''; ?>>Bonuses</option>
                            <option value="adjustment" <?php echo $filter_type === 'adjustment' ? 'selected' : ''; ?>>Adjustments</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="status">Status</label>
                        <select name="status" id="status" class="filter-select">
                            <option value="all" <?php echo $filter_status === 'all' ? 'selected' : ''; ?>>All Status</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="processing" <?php echo $filter_status === 'processing' ? 'selected' : ''; ?>>Processing</option>
                            <option value="completed" <?php echo $filter_status === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="failed" <?php echo $filter_status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                            <option value="cancelled" <?php echo $filter_status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="date_from">From Date</label>
                        <input type="date" name="date_from" id="date_from" value="<?php echo htmlspecialchars($filter_date_from); ?>" class="filter-input">
                    </div>
                    
                    <div class="filter-group">
                        <label for="date_to">To Date</label>
                        <input type="date" name="date_to" id="date_to" value="<?php echo htmlspecialchars($filter_date_to); ?>" class="filter-input">
                    </div>
                    
                    <div class="filter-actions">
                        <button type="submit" class="user-btn user-btn-primary">
                            <i class="icon-filter"></i>
                            Apply
                        </button>
                        <a href="?" class="user-btn user-btn-secondary">
                            <i class="icon-refresh"></i>
                            Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Transaction Statement -->
    <div class="transaction-statement user-fade-in">
        <div class="statement-header">
            <h3 class="statement-title">Transaction Statement</h3>
            <div class="statement-info">
                <span class="total-count"><?php echo $total_transactions; ?> transactions found</span>
            </div>
        </div>
        
        <?php if (!empty($transactions)): ?>
        <div class="transaction-table-container">
            <table class="transaction-table">
                <thead>
                    <tr>
                        <th>Date & Time</th>
                        <th>Transaction ID</th>
                        <th>Type</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Balance Before</th>
                        <th>Balance After</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transactions as $transaction): ?>
                    <tr class="transaction-row">
                        <td class="date-cell">
                            <div class="date-info">
                                <span class="date"><?php echo date('M j, Y', strtotime($transaction['created_at'])); ?></span>
                                <span class="time"><?php echo date('H:i:s', strtotime($transaction['created_at'])); ?></span>
                            </div>
                        </td>
                        <td class="id-cell">
                            <span class="transaction-id"><?php echo htmlspecialchars($transaction['transaction_id'] ?? $transaction['order_no'] ?? 'N/A'); ?></span>
                        </td>
                        <td class="type-cell">
                            <div class="type-info">
                                <i class="icon-<?php echo $transaction['type']; ?>"></i>
                                <span class="type-name"><?php echo $transaction['display_type']; ?></span>
                            </div>
                        </td>
                        <td class="amount-cell">
                            <span class="amount <?php echo ($transaction['amount'] >= 0) ? 'positive' : 'negative'; ?>">
                                <?php echo ($transaction['amount'] >= 0) ? '+' : ''; ?><?php echo formatCurrency($transaction['amount']); ?>
                            </span>
                        </td>
                        <td class="status-cell">
                            <span class="status-badge status-<?php echo $transaction['status']; ?>">
                                <?php echo ucfirst($transaction['status']); ?>
                            </span>
                        </td>
                        <td class="balance-cell">
                            <span class="balance-amount"><?php echo formatCurrency($transaction['balance_before'] ?? 0); ?></span>
                        </td>
                        <td class="balance-cell">
                            <span class="balance-amount"><?php echo formatCurrency($transaction['balance_after'] ?? 0); ?></span>
                        </td>
                        <td class="details-cell">
                            <button class="details-btn" onclick="showTransactionDetails(<?php echo $transaction['id']; ?>)">
                                <i class="icon-info"></i>
                                View
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="pagination-container">
            <div class="pagination">
                <?php if ($page > 1): ?>
                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" class="pagination-btn prev-btn">
                    <i class="icon-chevron-left"></i>
                    Previous
                </a>
                <?php endif; ?>

                <div class="pagination-numbers">
                    <?php
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);

                    if ($start_page > 1): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>" class="pagination-number">1</a>
                        <?php if ($start_page > 2): ?>
                            <span class="pagination-dots">...</span>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                           class="pagination-number <?php echo $i === $page ? 'active' : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($end_page < $total_pages): ?>
                        <?php if ($end_page < $total_pages - 1): ?>
                            <span class="pagination-dots">...</span>
                        <?php endif; ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>" class="pagination-number"><?php echo $total_pages; ?></a>
                    <?php endif; ?>
                </div>

                <?php if ($page < $total_pages): ?>
                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" class="pagination-btn next-btn">
                    Next
                    <i class="icon-chevron-right"></i>
                </a>
                <?php endif; ?>
            </div>

            <div class="pagination-info">
                Showing <?php echo $offset + 1; ?>-<?php echo min($offset + $per_page, $total_transactions); ?>
                of <?php echo $total_transactions; ?> transactions
            </div>
        </div>
        <?php endif; ?>
        
        <?php else: ?>
        <div class="no-transactions">
            <div class="empty-state">
                <i class="icon-transactions"></i>
                <h3>No Transactions Found</h3>
                <p>No transactions match your current filters. Try adjusting your search criteria.</p>
                <div class="empty-actions">
                    <a href="?" class="user-btn user-btn-secondary">Clear Filters</a>
                    <a href="../deposit/" class="user-btn user-btn-primary">Make a Deposit</a>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions user-fade-in">
        <h3 class="actions-title">Quick Actions</h3>
        <div class="action-buttons">
            <a href="../deposit/" class="action-btn deposit-btn">
                <i class="icon-deposit"></i>
                <span>Make Deposit</span>
            </a>
            <a href="../withdraw/" class="action-btn withdraw-btn">
                <i class="icon-withdraw"></i>
                <span>Withdraw Funds</span>
            </a>
            <a href="../profile/" class="action-btn profile-btn">
                <i class="icon-user"></i>
                <span>View Profile</span>
            </a>
            <a href="../dashboard/" class="action-btn dashboard-btn">
                <i class="icon-dashboard"></i>
                <span>Dashboard</span>
            </a>
        </div>
    </div>
</div>

<!-- Transaction Details Modal -->
<div id="transactionDetailsModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Transaction Details</h3>
            <span class="modal-close">&times;</span>
        </div>
        <div class="modal-body">
            <div id="transactionDetailsContent">
                <!-- Content will be loaded dynamically -->
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="user-btn user-btn-secondary" onclick="closeTransactionDetails()">Close</button>
        </div>
    </div>
</div>

<?php
// Include footer
include '../includes/user_footer.php';
?>