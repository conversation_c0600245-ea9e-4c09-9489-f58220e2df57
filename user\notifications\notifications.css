/**
 * Bamboo User Application - Notifications Page Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* Hero Section */
.notifications-hero {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    padding: 3rem 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.hero-content {
    max-width: 600px;
    margin: 0 auto;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-description {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .notifications-hero {
        padding: 2rem 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }
}



/* Notifications Actions */
.notifications-actions {
    margin-bottom: var(--user-spacing-md);
}

.actions-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notifications-summary {
    color: var(--user-text-secondary);
    font-size: var(--user-font-size-sm);
}

/* Notifications List */
.notifications-list {
    display: flex;
    flex-direction: column;
    gap: var(--user-spacing-sm);
    margin-bottom: var(--user-spacing-lg);
}

.notification-item {
    background: white;
    border: 1px solid var(--user-border-color);
    border-radius: var(--user-border-radius);
    padding: var(--user-spacing-md);
    position: relative;
    transition: all 0.3s ease;
}

.notification-item:hover {
    border-color: var(--user-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notification-item.unread {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.notification-item.read {
    background: white;
    opacity: 0.9;
}

.notification-content {
    width: 100%;
}

.notification-header {
    display: flex;
    align-items: center;
    gap: var(--user-spacing-sm);
    margin-bottom: var(--user-spacing-sm);
}

.notification-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--user-primary), var(--user-primary-dark));
    border-radius: var(--user-border-radius);
    color: white;
    flex-shrink: 0;
}

.notification-meta {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--user-spacing-xs);
}

.notification-type {
    font-weight: 600;
    color: var(--user-primary);
    font-size: var(--user-font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: rgba(59, 130, 246, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    display: inline-block;
}

.notification-time {
    font-size: var(--user-font-size-xs);
    color: #374151;
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-weight: 500;
    border: 1px solid #e5e7eb;
}

.notification-actions {
    display: flex;
    gap: var(--user-spacing-xs);
}

.action-btn {
    background: none;
    border: none;
    padding: var(--user-spacing-xs);
    border-radius: var(--user-border-radius-sm);
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    opacity: 0.6;
}

.action-btn:hover {
    opacity: 1;
    background: var(--user-bg-light);
}



.delete-btn:hover {
    background: var(--user-danger-bg);
}

.notification-message {
    font-size: var(--user-font-size-md);
    line-height: 1.6;
    color: var(--user-text-primary);
    margin-bottom: var(--user-spacing-xs);
}

.notification-title {
    font-size: var(--user-font-size-sm);
    color: #374151;
    font-weight: 600;
    background: rgba(156, 163, 175, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    display: inline-block;
    margin-top: 0.5rem;
}

.unread-indicator {
    position: absolute;
    top: var(--user-spacing-sm);
    right: var(--user-spacing-sm);
    width: 8px;
    height: 8px;
    background: var(--user-primary);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* No Notifications State */
.no-notifications {
    text-align: center;
    padding: var(--user-spacing-xxl);
    color: var(--user-text-secondary);
}

.no-notifications-icon {
    font-size: 4rem;
    margin-bottom: var(--user-spacing-lg);
    opacity: 0.3;
}

.no-notifications h5 {
    font-size: var(--user-font-size-xl);
    font-weight: 600;
    margin-bottom: var(--user-spacing-md);
    color: var(--user-text-primary);
}

.no-notifications p {
    font-size: var(--user-font-size-md);
    line-height: 1.6;
    max-width: 500px;
    margin: 0 auto;
}

/* Pagination */
.notifications-pagination {
    margin-top: var(--user-spacing-lg);
}

.pagination {
    margin: 0;
}

.page-link {
    color: var(--user-primary);
    border-color: var(--user-border-color);
    padding: var(--user-spacing-sm) var(--user-spacing-md);
}

.page-link:hover {
    color: var(--user-primary-dark);
    background-color: var(--user-bg-light);
    border-color: var(--user-primary);
}

.page-item.active .page-link {
    background-color: var(--user-primary);
    border-color: var(--user-primary);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .notifications-header {
        padding: var(--user-spacing-lg) var(--user-spacing-md);
    }
    
    .header-content {
        flex-direction: column;
        gap: var(--user-spacing-md);
        text-align: center;
    }
    
    .header-stats {
        justify-content: center;
        gap: var(--user-spacing-md);
    }
    
    .actions-row {
        flex-direction: column;
        gap: var(--user-spacing-sm);
        align-items: stretch;
    }
    
    .actions-right {
        text-align: center;
    }
    
    .notification-header {
        flex-wrap: wrap;
    }
    
    .notification-actions {
        order: -1;
        margin-left: auto;
    }
    
    .notification-meta {
        width: 100%;
    }
    
    .stat-item {
        min-width: 60px;
        padding: var(--user-spacing-sm);
    }
    
    .stat-number {
        font-size: var(--user-font-size-lg);
    }
}
