<?php
define('BAMBOO_APP', true);
require_once 'includes/config.php';
require_once 'includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check admin login
if (!isAdminLoggedIn()) {
    echo "<h2>Admin Not Logged In</h2>";
    echo "<p>Please <a href='admin/login/'>login to admin panel</a> first.</p>";
    exit;
}

echo "<h2>Admin Recharge Test - Direct Processing</h2>";
echo "<p>Admin ID: " . $_SESSION['admin_id'] . "</p>";
echo "<p>Admin Username: " . $_SESSION['admin_username'] . "</p>";

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>Processing Request...</h3>";
    
    $action = $_POST['action'] ?? '';
    $transaction_id = (int)($_POST['transaction_id'] ?? 0);
    
    echo "<p>Action: $action</p>";
    echo "<p>Transaction ID: $transaction_id</p>";
    
    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        echo "<p style='color: red;'>❌ CSRF token verification failed</p>";
    } else {
        echo "<p style='color: green;'>✅ CSRF token verified</p>";
        
        if ($action === 'approve' && $transaction_id > 0) {
            echo "<h4>Processing Approval...</h4>";
            
            try {
                // Get transaction
                $transaction = fetchRow('SELECT * FROM transactions WHERE id = ?', [$transaction_id]);
                if (!$transaction) {
                    throw new Exception('Transaction not found');
                }
                
                echo "<p>Transaction found: ID {$transaction['id']}, Amount: {$transaction['amount']}, Status: {$transaction['status']}</p>";
                
                if ($transaction['status'] !== 'pending') {
                    throw new Exception('Transaction is not pending');
                }
                
                // Get user
                $user = fetchRow('SELECT id, balance FROM users WHERE id = ?', [$transaction['user_id']]);
                if (!$user) {
                    throw new Exception('User not found');
                }
                
                echo "<p>User found: ID {$user['id']}, Current Balance: {$user['balance']}</p>";
                
                $old_balance = $user['balance'];
                $new_balance = $old_balance + $transaction['amount'];
                
                echo "<p>New balance will be: $new_balance</p>";
                
                // Update user balance
                $balance_updated = updateRecord('users', ['balance' => $new_balance], 'id = ?', [$user['id']]);
                if (!$balance_updated) {
                    throw new Exception('Failed to update user balance');
                }
                echo "<p style='color: green;'>✅ User balance updated</p>";
                
                // Update transaction
                $admin_id = $_SESSION['admin_id'];
                $update_data = [
                    'status' => 'completed',
                    'balance_before' => $old_balance,
                    'balance_after' => $new_balance,
                    'processed_by' => $admin_id,
                    'processed_at' => date('Y-m-d H:i:s')
                ];
                
                $transaction_updated = updateRecord('transactions', $update_data, 'id = ?', [$transaction_id]);
                if (!$transaction_updated) {
                    throw new Exception('Failed to update transaction');
                }
                echo "<p style='color: green;'>✅ Transaction updated to completed</p>";
                
                echo "<h4 style='color: green;'>✅ APPROVAL SUCCESSFUL!</h4>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
            }
            
        } elseif ($action === 'reject' && $transaction_id > 0) {
            echo "<h4>Processing Rejection...</h4>";
            
            try {
                $result = updateRecord('transactions', ['status' => 'failed'], 'id = ?', [$transaction_id]);
                if ($result) {
                    echo "<h4 style='color: green;'>✅ REJECTION SUCCESSFUL!</h4>";
                } else {
                    echo "<p style='color: red;'>❌ Failed to update transaction status</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<hr>";
}

// Get pending deposits
$pending_deposits = fetchAll("
    SELECT t.id, t.user_id, t.amount, t.status, t.created_at, u.username 
    FROM transactions t 
    LEFT JOIN users u ON t.user_id = u.id 
    WHERE t.type = 'deposit' AND t.status = 'pending' 
    ORDER BY t.created_at DESC 
    LIMIT 10
");

if (empty($pending_deposits)) {
    echo "<h3>No Pending Deposits</h3>";
    echo "<p>Creating a test deposit...</p>";
    
    // Create test deposit
    $test_user = fetchRow("SELECT id, username, balance FROM users ORDER BY id LIMIT 1");
    if ($test_user) {
        $test_amount = 50.00;
        $insert_data = [
            'user_id' => $test_user['id'],
            'type' => 'deposit',
            'amount' => $test_amount,
            'balance_before' => $test_user['balance'],
            'balance_after' => $test_user['balance'],
            'status' => 'pending',
            'payment_method' => 'Test Payment',
            'transaction_id' => 'TEST_' . time(),
            'description' => 'Test deposit for admin approval'
        ];
        
        $new_id = insertRecord('transactions', $insert_data);
        if ($new_id) {
            echo "<p>✅ Created test deposit with ID: $new_id</p>";
            // Refresh the list
            $pending_deposits = fetchAll("
                SELECT t.id, t.user_id, t.amount, t.status, t.created_at, u.username 
                FROM transactions t 
                LEFT JOIN users u ON t.user_id = u.id 
                WHERE t.type = 'deposit' AND t.status = 'pending' 
                ORDER BY t.created_at DESC 
                LIMIT 10
            ");
        }
    }
}

if (!empty($pending_deposits)) {
    echo "<h3>Pending Deposits</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Status</th><th>Actions</th></tr>";
    
    foreach ($pending_deposits as $txn) {
        echo "<tr>";
        echo "<td>{$txn['id']}</td>";
        echo "<td>{$txn['username']}</td>";
        echo "<td>\${$txn['amount']}</td>";
        echo "<td>{$txn['status']}</td>";
        echo "<td>";
        
        // Approve form
        echo "<form method='POST' style='display: inline; margin-right: 10px;'>";
        echo "<input type='hidden' name='csrf_token' value='" . generateCSRFToken() . "'>";
        echo "<input type='hidden' name='transaction_id' value='{$txn['id']}'>";
        echo "<button type='submit' name='action' value='approve' style='background: green; color: white; border: none; padding: 5px 10px;'>Approve</button>";
        echo "</form>";
        
        // Reject form
        echo "<form method='POST' style='display: inline;'>";
        echo "<input type='hidden' name='csrf_token' value='" . generateCSRFToken() . "'>";
        echo "<input type='hidden' name='transaction_id' value='{$txn['id']}'>";
        echo "<button type='submit' name='action' value='reject' style='background: red; color: white; border: none; padding: 5px 10px;'>Reject</button>";
        echo "</form>";
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

?>