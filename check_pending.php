<?php
define('BAMBOO_APP', true);
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check for pending deposits
$pending = fetchAll('SELECT id, amount, status, created_at FROM transactions WHERE type = "deposit" AND status = "pending" LIMIT 5');
echo 'Pending deposits: ' . count($pending) . PHP_EOL;
foreach ($pending as $txn) {
    echo 'ID: ' . $txn['id'] . ', Amount: ' . $txn['amount'] . ', Status: ' . $txn['status'] . PHP_EOL;
}
?>