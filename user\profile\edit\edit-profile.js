/**
 * Bamboo User Application - Edit Profile Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

// Edit Profile page utilities
const EditProfileUtils = {
    // Initialize the edit profile page
    init: function() {
        this.setupFormValidation();
        this.setupPasswordStrength();
        this.setupPasswordConfirmation();
        this.setupPINConfirmation();
        this.setupAvatarPreview();
        console.log('Edit Profile page initialized successfully');
    },

    // Setup form validation
    setupFormValidation: function() {
        const form = document.getElementById('editProfileForm');
        if (!form) return;

        // Real-time validation
        const usernameInput = document.getElementById('username');
        const genderSelect = document.getElementById('gender');
        const currentPasswordInput = document.getElementById('current_password');
        const newPasswordInput = document.getElementById('new_password');
        const confirmPasswordInput = document.getElementById('confirm_password');
        const withdrawalPinInput = document.getElementById('withdrawal_pin');
        const confirmWithdrawalPinInput = document.getElementById('confirm_withdrawal_pin');

        if (usernameInput) {
            usernameInput.addEventListener('input', this.validateUsername.bind(this));
            usernameInput.addEventListener('blur', this.validateUsername.bind(this));
        }

        if (genderSelect) {
            genderSelect.addEventListener('change', this.validateGender.bind(this));
        }

        if (newPasswordInput) {
            newPasswordInput.addEventListener('input', this.validateNewPassword.bind(this));
            newPasswordInput.addEventListener('blur', this.validateNewPassword.bind(this));
        }

        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', this.validatePasswordConfirmation.bind(this));
            confirmPasswordInput.addEventListener('blur', this.validatePasswordConfirmation.bind(this));
        }

        if (withdrawalPinInput) {
            withdrawalPinInput.addEventListener('input', this.validateWithdrawalPIN.bind(this));
            withdrawalPinInput.addEventListener('blur', this.validateWithdrawalPIN.bind(this));
        }

        if (confirmWithdrawalPinInput) {
            confirmWithdrawalPinInput.addEventListener('input', this.validatePINConfirmation.bind(this));
            confirmWithdrawalPinInput.addEventListener('blur', this.validatePINConfirmation.bind(this));
        }

        // Form submission validation
        form.addEventListener('submit', this.validateForm.bind(this));
    },

    // Setup password strength indicator
    setupPasswordStrength: function() {
        const newPasswordInput = document.getElementById('new_password');
        if (!newPasswordInput) return;

        // Create password strength indicator
        const formGroup = newPasswordInput.closest('.form-group');
        if (!formGroup) return;

        const strengthIndicator = document.createElement('div');
        strengthIndicator.className = 'password-strength';
        strengthIndicator.innerHTML = '<div class="password-strength-bar"></div>';
        
        const helpText = formGroup.querySelector('.form-help');
        if (helpText) {
            helpText.parentNode.insertBefore(strengthIndicator, helpText);
        } else {
            formGroup.appendChild(strengthIndicator);
        }

        newPasswordInput.addEventListener('input', (e) => {
            this.updatePasswordStrength(e.target.value, strengthIndicator);
        });
    },

    // Update password strength indicator
    updatePasswordStrength: function(password, indicator) {
        if (!password) {
            indicator.className = 'password-strength';
            return;
        }

        let strength = 0;
        
        // Length check
        if (password.length >= 6) strength++;
        if (password.length >= 8) strength++;
        
        // Character variety checks
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;

        // Set strength class
        if (strength <= 2) {
            indicator.className = 'password-strength weak';
        } else if (strength <= 4) {
            indicator.className = 'password-strength medium';
        } else {
            indicator.className = 'password-strength strong';
        }
    },

    // Setup password confirmation validation
    setupPasswordConfirmation: function() {
        const newPasswordInput = document.getElementById('new_password');
        const confirmPasswordInput = document.getElementById('confirm_password');
        const currentPasswordInput = document.getElementById('current_password');

        if (!newPasswordInput || !confirmPasswordInput) return;

        // Show/hide current password requirement
        const updateCurrentPasswordRequirement = () => {
            const isChangingPassword = newPasswordInput.value.trim() !== '' || confirmPasswordInput.value.trim() !== '';
            
            if (currentPasswordInput) {
                if (isChangingPassword) {
                    currentPasswordInput.setAttribute('required', 'required');
                    currentPasswordInput.closest('.form-group').style.display = 'block';
                } else {
                    currentPasswordInput.removeAttribute('required');
                }
            }
        };

        newPasswordInput.addEventListener('input', updateCurrentPasswordRequirement);
        confirmPasswordInput.addEventListener('input', updateCurrentPasswordRequirement);
    },

    // Setup PIN confirmation validation
    setupPINConfirmation: function() {
        const withdrawalPinInput = document.getElementById('withdrawal_pin');
        const confirmWithdrawalPinInput = document.getElementById('confirm_withdrawal_pin');

        if (!withdrawalPinInput || !confirmWithdrawalPinInput) return;

        // Show/hide PIN confirmation requirement
        const updatePINConfirmationRequirement = () => {
            const isChangingPIN = withdrawalPinInput.value.trim() !== '';
            
            if (isChangingPIN) {
                confirmWithdrawalPinInput.setAttribute('required', 'required');
            } else {
                confirmWithdrawalPinInput.removeAttribute('required');
            }
        };

        withdrawalPinInput.addEventListener('input', updatePINConfirmationRequirement);
    },

    // Validate username
    validateUsername: function(event) {
        const field = event.target;
        const value = field.value.trim();
        
        let isValid = true;
        let message = '';

        if (value.length === 0) {
            isValid = false;
            message = 'Username is required';
        } else if (value.length < 3) {
            isValid = false;
            message = 'Username must be at least 3 characters';
        } else if (value.length > 50) {
            isValid = false;
            message = 'Username must be less than 50 characters';
        } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
            isValid = false;
            message = 'Username can only contain letters, numbers, and underscores';
        }

        this.setFieldValidation(field, isValid, message);
        return isValid;
    },

    // Validate gender
    validateGender: function(event) {
        const field = event.target;
        const value = field.value;
        
        let isValid = true;
        let message = '';

        if (!value || !['male', 'female'].includes(value)) {
            isValid = false;
            message = 'Please select a valid gender';
        }

        this.setFieldValidation(field, isValid, message);
        return isValid;
    },

    // Validate new password
    validateNewPassword: function(event) {
        const field = event.target;
        const value = field.value;
        
        // If empty, it's valid (optional field)
        if (value === '') {
            this.setFieldValidation(field, true, '');
            return true;
        }

        let isValid = true;
        let message = '';

        if (value.length < 6) {
            isValid = false;
            message = 'Password must be at least 6 characters';
        }

        this.setFieldValidation(field, isValid, message);
        return isValid;
    },

    // Validate password confirmation
    validatePasswordConfirmation: function(event) {
        const field = event.target;
        const value = field.value;
        const newPassword = document.getElementById('new_password').value;
        
        // If both are empty, it's valid
        if (value === '' && newPassword === '') {
            this.setFieldValidation(field, true, '');
            return true;
        }

        let isValid = true;
        let message = '';

        if (value !== newPassword) {
            isValid = false;
            message = 'Passwords do not match';
        }

        this.setFieldValidation(field, isValid, message);
        return isValid;
    },

    // Validate withdrawal PIN
    validateWithdrawalPIN: function(event) {
        const field = event.target;
        const value = field.value;
        
        // If empty, it's valid (optional field)
        if (value === '') {
            this.setFieldValidation(field, true, '');
            return true;
        }

        let isValid = true;
        let message = '';

        if (value.length < 4) {
            isValid = false;
            message = 'PIN must be at least 4 characters';
        }

        this.setFieldValidation(field, isValid, message);
        return isValid;
    },

    // Validate PIN confirmation
    validatePINConfirmation: function(event) {
        const field = event.target;
        const value = field.value;
        const withdrawalPin = document.getElementById('withdrawal_pin').value;
        
        // If both are empty, it's valid
        if (value === '' && withdrawalPin === '') {
            this.setFieldValidation(field, true, '');
            return true;
        }

        let isValid = true;
        let message = '';

        if (value !== withdrawalPin) {
            isValid = false;
            message = 'PINs do not match';
        }

        this.setFieldValidation(field, isValid, message);
        return isValid;
    },

    // Set field validation state
    setFieldValidation: function(field, isValid, message) {
        const formGroup = field.closest('.form-group');
        if (!formGroup) return;

        // Remove existing validation classes and messages
        field.classList.remove('is-valid', 'is-invalid');
        const existingFeedback = formGroup.querySelector('.invalid-feedback, .valid-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }

        if (field.value.trim() === '') {
            // No validation styling for empty fields
            return;
        }

        // Add validation classes and messages
        if (isValid) {
            field.classList.add('is-valid');
        } else {
            field.classList.add('is-invalid');
            if (message) {
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = message;
                formGroup.appendChild(feedback);
            }
        }
    },

    // Validate entire form
    validateForm: function(event) {
        const form = event.target;
        const usernameInput = document.getElementById('username');
        const genderSelect = document.getElementById('gender');
        const newPasswordInput = document.getElementById('new_password');
        const confirmPasswordInput = document.getElementById('confirm_password');
        const withdrawalPinInput = document.getElementById('withdrawal_pin');
        const confirmWithdrawalPinInput = document.getElementById('confirm_withdrawal_pin');

        let isValid = true;

        // Validate required fields
        if (usernameInput && !this.validateUsername({ target: usernameInput })) {
            isValid = false;
        }

        if (genderSelect && !this.validateGender({ target: genderSelect })) {
            isValid = false;
        }

        // Validate password fields if changing password
        if (newPasswordInput && newPasswordInput.value.trim() !== '') {
            if (!this.validateNewPassword({ target: newPasswordInput })) {
                isValid = false;
            }
            if (confirmPasswordInput && !this.validatePasswordConfirmation({ target: confirmPasswordInput })) {
                isValid = false;
            }
        }

        // Validate PIN fields if changing PIN
        if (withdrawalPinInput && withdrawalPinInput.value.trim() !== '') {
            if (!this.validateWithdrawalPIN({ target: withdrawalPinInput })) {
                isValid = false;
            }
            if (confirmWithdrawalPinInput && !this.validatePINConfirmation({ target: confirmWithdrawalPinInput })) {
                isValid = false;
            }
        }

        if (!isValid) {
            event.preventDefault();
            this.showFormError('Please correct the errors above before submitting.');
            return false;
        }

        // Show loading state
        this.showLoadingState(form);
        return true;
    },

    // Show form error
    showFormError: function(message) {
        // Remove existing error messages
        const existingError = document.querySelector('.form-error-message');
        if (existingError) {
            existingError.remove();
        }

        // Create and show error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'form-error-message alert alert-danger';
        errorDiv.textContent = message;

        const form = document.getElementById('editProfileForm');
        if (form) {
            form.insertBefore(errorDiv, form.firstChild);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 5000);
        }
    },

    // Show loading state
    showLoadingState: function(form) {
        form.classList.add('loading');
        
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.textContent = 'Updating...';
        }
    },

    // Setup avatar preview functionality
    setupAvatarPreview: function() {
        const avatarInput = document.getElementById('avatar_upload');
        const avatarPreview = document.getElementById('avatarPreview');

        if (!avatarInput || !avatarPreview) return;

        avatarInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPG, PNG, GIF)');
                    e.target.value = '';
                    return;
                }

                // Validate file size (2MB max)
                if (file.size > 2 * 1024 * 1024) {
                    alert('File size must be less than 2MB');
                    e.target.value = '';
                    return;
                }

                // Preview the image
                const reader = new FileReader();
                reader.onload = function(e) {
                    avatarPreview.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    EditProfileUtils.init();
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EditProfileUtils;
}
