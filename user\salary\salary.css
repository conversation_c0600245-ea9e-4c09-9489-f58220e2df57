/**
 * Bamboo User Application - Salary Page Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* Page Title */
.salary-page-title {
    background: linear-gradient(135deg, var(--user-primary) 0%, var(--user-gradient-end) 100%);
    color: white;
    padding: var(--user-spacing-xl);
    border-radius: var(--user-border-radius-lg);
    margin-bottom: var(--user-spacing-xl);
    text-align: center;
}

.salary-page-title .page-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.salary-page-title .page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

/* Salary Statistics Grid */
.salary-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--user-spacing-lg);
    margin-bottom: var(--user-spacing-xl);
}

.stat-card {
    background: white;
    border-radius: var(--user-border-radius-lg);
    padding: var(--user-spacing-lg);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--user-border-color);
    display: flex;
    align-items: center;
    gap: var(--user-spacing-md);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 2.5rem;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--user-text-primary);
    margin: 0 0 0.25rem 0;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--user-text-secondary);
    margin: 0;
    font-weight: 500;
}

/* Specific stat card colors - removed left borders */

/* Salary History Card */
.salary-history-card {
    margin-bottom: var(--user-spacing-xl);
}

.history-count {
    background: rgba(var(--user-primary-rgb), 0.1);
    color: var(--user-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* No Salary Data */
.no-salary-data {
    text-align: center;
    padding: var(--user-spacing-xl);
    color: var(--user-text-secondary);
}

.no-data-icon {
    font-size: 4rem;
    margin-bottom: var(--user-spacing-md);
}

.no-salary-data h6 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--user-text-primary);
    margin-bottom: var(--user-spacing-sm);
}

.no-salary-data p {
    font-size: 1rem;
    line-height: 1.5;
    max-width: 400px;
    margin: 0 auto;
}

/* Salary Transactions */
.salary-transactions {
    display: flex;
    flex-direction: column;
    gap: var(--user-spacing-md);
}

.salary-transaction-item {
    display: flex;
    align-items: center;
    padding: var(--user-spacing-lg);
    background: var(--user-bg-secondary);
    border-radius: var(--user-border-radius-lg);
    border: 1px solid var(--user-border-color);
    transition: all 0.3s ease;
}

.salary-transaction-item:hover {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.transaction-info {
    display: flex;
    align-items: center;
    gap: var(--user-spacing-md);
    flex: 1;
}

.transaction-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.transaction-details {
    flex: 1;
}

.transaction-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--user-text-primary);
    margin-bottom: 0.25rem;
}

.transaction-date {
    font-size: 0.875rem;
    color: var(--user-text-secondary);
    margin-bottom: 0.25rem;
}

.transaction-notes {
    font-size: 0.875rem;
    color: var(--user-text-secondary);
    font-style: italic;
    margin-top: 0.25rem;
}

.transaction-amount {
    text-align: right;
    margin-right: var(--user-spacing-lg);
}

.amount-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #10b981;
}

.transaction-status {
    flex-shrink: 0;
}

/* Status Badges */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-paid {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.status-pending_approval {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

/* Back to Profile */
.back-to-profile {
    text-align: center;
    margin-bottom: var(--user-spacing-xl);
}

/* Responsive Design */
@media (max-width: 768px) {
    .salary-page-title .page-title {
        font-size: 1.5rem;
    }
    
    .salary-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--user-spacing-md);
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--user-spacing-sm);
    }
    
    .salary-transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--user-spacing-md);
    }
    
    .transaction-info {
        width: 100%;
    }
    
    .transaction-amount {
        margin-right: 0;
        text-align: left;
    }
    
    .transaction-status {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .salary-stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Animation */
.user-fade-in {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
