<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

echo "=== USERS TABLE STRUCTURE ===\n";
try {
    $result = executeQuery('DESCRIBE users');
    foreach($result as $row) {
        echo $row['Field'] . ' - ' . $row['Type'] . "\n";
    }
} catch(Exception $e) {
    echo 'Error: ' . $e->getMessage() . "\n";
}

echo "\n=== PAYMENT_CARDS TABLE STRUCTURE ===\n";
try {
    $result = executeQuery('DESCRIBE payment_cards');
    foreach($result as $row) {
        echo $row['Field'] . ' - ' . $row['Type'] . "\n";
    }
} catch(Exception $e) {
    echo 'Error: ' . $e->getMessage() . "\n";
}

echo "\n=== SAMPLE USER DATA ===\n";
try {
    $result = executeQuery('SELECT id, username, referral_code, credit_score, full_name FROM users LIMIT 1');
    foreach($result as $row) {
        print_r($row);
    }
} catch(Exception $e) {
    echo 'Error: ' . $e->getMessage() . "\n";
}
?>