/**
 * Bamboo User Dashboard - Master CSS
 * Company: Notepadsly
 * Version: 1.0
 * Description: Universal styling system for PC user dashboard with white background theme
 */

/* ===== CSS VARIABLES - DYNAMIC THEME INTEGRATION ===== */
:root {
    /* Dynamic colors from appearance system - will be updated by JavaScript */
    --user-primary: #ff6900;
    --user-secondary: #ffffff;
    --user-accent: #007bff;
    --user-gradient-start: #ff6900;
    --user-gradient-end: #ff8533;
    
    /* Fixed user dashboard colors */
    --user-background: #ffffff;
    --user-surface: #ffffff;
    --user-card-bg: #ffffff;
    --user-text-primary: #2c3e50;
    --user-text-secondary: #6c757d;
    --user-text-muted: #95a5a6;
    
    /* Status colors */
    --user-success: #28a745;
    --user-warning: #ffc107;
    --user-danger: #dc3545;
    --user-info: #17a2b8;
    
    /* Border and spacing */
    --user-border-color: #e9ecef;
    --user-border-light: #f8f9fa;
    --user-border-radius: 8px;
    --user-border-radius-lg: 12px;
    --user-border-width: 1px;
    
    /* Shadows - subtle only */
    --user-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.08);
    --user-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --user-shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.12);
    
    /* Spacing system */
    --user-spacing-xs: 0.25rem;
    --user-spacing-sm: 0.5rem;
    --user-spacing: 1rem;
    --user-spacing-lg: 1.5rem;
    --user-spacing-xl: 2rem;
    --user-spacing-xxl: 3rem;
    
    /* Typography */
    --user-font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --user-font-size-sm: 0.875rem;
    --user-font-size: 1rem;
    --user-font-size-lg: 1.125rem;
    --user-font-size-xl: 1.25rem;
    --user-font-size-xxl: 1.5rem;
    --user-line-height: 1.6;
    
    /* Transitions */
    --user-transition: all 0.3s ease;
    --user-transition-fast: all 0.15s ease;
}

/* ===== GLOBAL RESET AND BASE STYLES ===== */
* {
    box-sizing: border-box;
}

body.user-dashboard {
    background-color: var(--user-background);
    font-family: var(--user-font-family);
    font-size: var(--user-font-size);
    line-height: var(--user-line-height);
    color: var(--user-text-primary);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== LAYOUT CONTAINERS ===== */
.user-container {
    width: 100%;
    padding: 0 2rem;
}

/* Full width layout with responsive padding */
@media (min-width: 1400px) {
    .user-container {
        padding: 0 4rem;
    }
}

.user-container-fluid {
    width: 100%;
    padding: 0 2rem;
}

/* Full width layout with responsive padding */
@media (min-width: 1400px) {
    .user-container-fluid {
        padding: 0 4rem;
    }
}

.user-main-content {
    background-color: var(--user-background);
    min-height: calc(100vh - 140px); /* Account for header/footer */
    padding: var(--user-spacing-lg) 0;
}

/* ===== GRID SYSTEM ===== */
.user-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(-1 * var(--user-spacing-sm));
}

.user-col {
    flex: 1;
    padding: 0 var(--user-spacing-sm);
}

.user-col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.user-col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.user-col-3 { flex: 0 0 25%; max-width: 25%; }
.user-col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.user-col-6 { flex: 0 0 50%; max-width: 50%; }
.user-col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.user-col-9 { flex: 0 0 75%; max-width: 75%; }
.user-col-12 { flex: 0 0 100%; max-width: 100%; }

/* ===== CARD COMPONENTS ===== */
.user-card {
    background-color: var(--user-card-bg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--user-border-radius);
    box-shadow: var(--user-shadow-sm);
    margin-bottom: var(--user-spacing-lg);
    transition: var(--user-transition);
}

.user-card:hover {
    box-shadow: var(--user-shadow);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.9));
}

.user-card-header {
    padding: var(--user-spacing-lg);
    border-bottom: var(--user-border-width) solid var(--user-border-light);
    background-color: var(--user-surface);
    border-radius: var(--user-border-radius) var(--user-border-radius) 0 0;
}

.user-card-body {
    padding: var(--user-spacing-lg);
}

.user-card-footer {
    padding: var(--user-spacing);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0 0 var(--user-border-radius) var(--user-border-radius);
}

.user-card-title {
    margin: 0 0 var(--user-spacing-sm) 0;
    font-size: var(--user-font-size-lg);
    font-weight: 600;
    color: var(--user-text-primary);
}

/* ===== BUTTON COMPONENTS ===== */
.user-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--user-spacing-sm) var(--user-spacing-lg);
    font-size: var(--user-font-size);
    font-weight: 500;
    text-decoration: none;
    border: var(--user-border-width) solid transparent;
    border-radius: var(--user-border-radius);
    cursor: pointer;
    transition: var(--user-transition);
    min-height: 40px;
    gap: var(--user-spacing-xs);
}

.user-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--user-shadow);
}

.user-btn:active {
    transform: translateY(0);
}

.user-btn-primary {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    border-color: var(--user-primary);
}

.user-btn-secondary {
    background-color: var(--user-secondary);
    color: var(--user-text-primary);
    border-color: var(--user-border-color);
}

.user-btn-success {
    background-color: var(--user-success);
    color: white;
    border-color: var(--user-success);
}

.user-btn-warning {
    background-color: var(--user-warning);
    color: var(--user-text-primary);
    border-color: var(--user-warning);
}

.user-btn-danger {
    background-color: var(--user-danger);
    color: white;
    border-color: var(--user-danger);
}

.user-btn-outline {
    background-color: transparent;
    color: var(--user-primary);
    border-color: var(--user-primary);
}

.user-btn-outline:hover {
    background-color: var(--user-primary);
    color: white;
}

.user-btn-sm {
    padding: var(--user-spacing-xs) var(--user-spacing);
    font-size: var(--user-font-size-sm);
    min-height: 32px;
}

.user-btn-lg {
    padding: var(--user-spacing) var(--user-spacing-xl);
    font-size: var(--user-font-size-lg);
    min-height: 48px;
}

/* ===== FORM COMPONENTS ===== */
.user-form-group {
    margin-bottom: var(--user-spacing-lg);
}

.user-form-label {
    display: block;
    margin-bottom: var(--user-spacing-xs);
    font-weight: 500;
    color: var(--user-text-primary);
}

.user-form-control {
    width: 100%;
    padding: var(--user-spacing-sm) var(--user-spacing);
    font-size: var(--user-font-size);
    border: var(--user-border-width) solid var(--user-border-color);
    border-radius: var(--user-border-radius);
    background-color: var(--user-surface);
    transition: var(--user-transition);
    min-height: 40px;
}

.user-form-control:focus {
    outline: none;
    border-color: var(--user-primary);
    box-shadow: 0 0 0 2px rgba(255, 105, 0, 0.1);
}

.user-form-control::placeholder {
    color: var(--user-text-muted);
}

/* ===== UTILITY CLASSES ===== */
.user-text-center { text-align: center; }
.user-text-left { text-align: left; }
.user-text-right { text-align: right; }

.user-text-primary { color: var(--user-text-primary); }
.user-text-secondary { color: var(--user-text-secondary); }
.user-text-muted { color: var(--user-text-muted); }
.user-text-success { color: var(--user-success); }
.user-text-warning { color: var(--user-warning); }
.user-text-danger { color: var(--user-danger); }

.user-bg-primary { background-color: var(--user-primary); }
.user-bg-secondary { background-color: var(--user-secondary); }
.user-bg-success { background-color: var(--user-success); }
.user-bg-warning { background-color: var(--user-warning); }
.user-bg-danger { background-color: var(--user-danger); }

.user-mb-0 { margin-bottom: 0; }
.user-mb-1 { margin-bottom: var(--user-spacing-xs); }
.user-mb-2 { margin-bottom: var(--user-spacing-sm); }
.user-mb-3 { margin-bottom: var(--user-spacing); }
.user-mb-4 { margin-bottom: var(--user-spacing-lg); }
.user-mb-5 { margin-bottom: var(--user-spacing-xl); }

.user-mt-0 { margin-top: 0; }
.user-mt-1 { margin-top: var(--user-spacing-xs); }
.user-mt-2 { margin-top: var(--user-spacing-sm); }
.user-mt-3 { margin-top: var(--user-spacing); }
.user-mt-4 { margin-top: var(--user-spacing-lg); }
.user-mt-5 { margin-top: var(--user-spacing-xl); }

.user-p-0 { padding: 0; }
.user-p-1 { padding: var(--user-spacing-xs); }
.user-p-2 { padding: var(--user-spacing-sm); }
.user-p-3 { padding: var(--user-spacing); }
.user-p-4 { padding: var(--user-spacing-lg); }
.user-p-5 { padding: var(--user-spacing-xl); }

.user-d-none { display: none; }
.user-d-block { display: block; }
.user-d-flex { display: flex; }
.user-d-inline { display: inline; }
.user-d-inline-block { display: inline-block; }

.user-flex-column { flex-direction: column; }
.user-flex-row { flex-direction: row; }
.user-justify-center { justify-content: center; }
.user-justify-between { justify-content: space-between; }
.user-align-center { align-items: center; }

.user-w-100 { width: 100%; }
.user-h-100 { height: 100%; }

/* ===== ANIMATIONS ===== */
@keyframes user-fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes user-slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

.user-fade-in {
    animation: user-fadeIn 0.3s ease-out;
}

.user-slide-in {
    animation: user-slideIn 0.3s ease-out;
}

/* ===== LOADING STATES ===== */
.user-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.user-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--user-border-color);
    border-top-color: var(--user-primary);
    border-radius: 50%;
    animation: user-spin 1s linear infinite;
}

@keyframes user-spin {
    to { transform: rotate(360deg); }
}

/* ===== HEADER STYLES ===== */
.user-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    height: 70px;
}

.user-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    width: 100%;
    padding: 0 2rem;
}

/* Full width layout with responsive padding */
@media (min-width: 1400px) {
    .user-header-content {
        padding: 0 4rem;
    }
}

.user-header-logo .logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--user-text-primary);
}

.user-header-logo .logo-img {
    height: 40px;
    width: auto;
}

.user-header-logo .logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation Styles */
.user-nav {
    flex: 1;
    margin: 0 var(--user-spacing-xl);
}

.user-nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--user-spacing);
    justify-content: center;
}

.user-nav-link {
    display: flex;
    align-items: center;
    padding: var(--user-spacing-sm) var(--user-spacing-lg);
    text-decoration: none;
    color: var(--user-text-primary);
    border-radius: var(--user-border-radius);
    transition: var(--user-transition);
    gap: var(--user-spacing-xs);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.6));
    border: 1px solid rgba(0, 0, 0, 0.08);
    font-weight: 500;
}

.user-nav-link:hover {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.user-nav-link i {
    font-size: var(--user-font-size-lg);
}

/* Enhanced Navigation Dropdown */
.user-nav-dropdown {
    position: relative;
}

.nav-dropdown-toggle {
    background: none !important;
    border: none !important;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--user-spacing-xs);
}

.nav-chevron {
    margin-left: auto;
    margin-right: 0;
    font-size: 0.75rem;
    transition: transform 0.3s ease;
}

.user-nav-dropdown.active .nav-chevron {
    transform: rotate(180deg);
}

.user-nav-dropdown .nav-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    padding: 0.5rem 0;
    z-index: 1000;
    display: none;
    border: 1px solid #e5e7eb;
    margin-top: 0.5rem;
}

.user-nav-dropdown.active .nav-dropdown-menu {
    display: block;
    animation: navDropdownFadeIn 0.3s ease-out;
}

.nav-dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #374151;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.nav-dropdown-item:hover {
    background: #f8fafc;
    color: var(--user-primary);
    text-decoration: none;
}



.nav-dropdown-item i {
    width: 16px;
    text-align: center;
    margin-right: 0;
    font-size: 1rem;
}

@keyframes navDropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Navigation Dropdown */
.user-nav-item.dropdown {
    position: relative;
}

.nav-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--user-surface);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--user-border-radius);
    box-shadow: var(--user-shadow-lg);
    min-width: 200px;
    z-index: 1001;
    display: none;
    padding: var(--user-spacing-xs) 0;
}

.user-nav-item.dropdown:hover .nav-dropdown-menu {
    display: block;
}

.nav-dropdown-menu .dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--user-spacing-sm);
    padding: var(--user-spacing-sm) var(--user-spacing);
    text-decoration: none;
    color: var(--user-text-primary);
    transition: var(--user-transition);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.nav-dropdown-menu .dropdown-item:last-child {
    border-bottom: none;
}

.nav-dropdown-menu .dropdown-item:hover {
    background-color: var(--user-primary);
    color: white;
}

/* Header Info Section */
.user-header-info {
    display: flex;
    align-items: center;
    gap: var(--user-spacing-lg);
}

.user-balance-display {
    display: flex;
    align-items: center;
    padding: var(--user-spacing-sm) var(--user-spacing);
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.05));
    border: 1px solid rgba(40, 167, 69, 0.2);
    border-radius: var(--user-border-radius);
    font-size: var(--user-font-size-sm);
}

.balance-value {
    font-weight: 600;
    color: var(--user-success);
    font-size: var(--user-font-size);
}

.refresh-balance {
    padding: var(--user-spacing-xs);
    min-height: 32px;
    width: 32px;
}

/* Notifications Dropdown */
.user-notifications-dropdown {
    position: relative;
}

.notifications-toggle {
    position: relative;
    background: none;
    border: none;
    padding: var(--user-spacing-sm);
    border-radius: var(--user-border-radius);
    cursor: pointer;
    transition: var(--user-transition);
}

.notifications-toggle:hover {
    background-color: var(--user-border-light);
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--user-danger);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* User Profile Display */
.user-profile-display {
    display: flex;
    align-items: center;
    gap: var(--user-spacing-sm);
    padding: var(--user-spacing-xs) var(--user-spacing-sm);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
}

.avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.username-link {
    text-decoration: none;
    color: inherit;
    transition: var(--user-transition);
}

.username-link:hover {
    color: var(--user-primary);
    text-decoration: none;
}

.username {
    font-weight: 600;
    color: var(--user-text-primary);
    transition: var(--user-transition);
}

.username-link:hover .username {
    color: var(--user-primary);
}

.vip-level {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
}



.notifications-menu {
    width: 300px;
    right: 0;
}

.notifications-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--user-spacing);
    border-bottom: var(--user-border-width) solid var(--user-border-light);
}

.notifications-header h6 {
    margin: 0;
    font-weight: 600;
}

.view-all {
    color: var(--user-primary);
    text-decoration: none;
    font-size: var(--user-font-size-sm);
}



/* ===== HEADER RESPONSIVE ===== */
@media (max-width: 768px) {
    .user-header-content {
        padding: 0 var(--user-spacing-sm);
    }

    .user-nav {
        margin: 0 var(--user-spacing);
    }

    .user-nav-list {
        gap: var(--user-spacing-sm);
    }

    .user-nav-link {
        padding: var(--user-spacing-xs) var(--user-spacing-sm);
        font-size: var(--user-font-size-sm);
    }

    .user-nav-link span {
        display: none;
    }

    .user-header-info {
        gap: var(--user-spacing-sm);
    }

    .user-info {
        display: none;
    }
}

/* ===== FOOTER STYLES ===== */
.user-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    margin-top: auto;
    padding: var(--user-spacing-lg) 0;
}

.user-footer-content {
    width: 100%;
    padding: 0 2rem;
}

/* Full width layout with responsive padding */
@media (min-width: 1400px) {
    .user-footer-content {
        padding: 0 4rem;
    }
}

.footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-copyright p {
    margin: 0;
    color: var(--user-text-muted);
    font-size: var(--user-font-size-sm);
    line-height: 1.4;
}

.footer-brand {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: right;
}

.app-name {
    font-weight: 700;
    color: var(--user-primary);
    font-size: var(--user-font-size-lg);
    margin-bottom: 2px;
}

.app-tagline {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
}

@media (max-width: 768px) {
    .footer-info {
        flex-direction: column;
        text-align: center;
        gap: var(--user-spacing);
    }

    .footer-brand {
        align-items: center;
        text-align: center;
    }
}

/* ===== NOTIFICATION SYSTEM ===== */
.user-notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.user-notification {
    background-color: var(--user-surface);
    border: none !important;
    border-radius: var(--user-border-radius);
    box-shadow: var(--user-shadow-lg);
    margin-bottom: var(--user-spacing-sm);
    overflow: hidden;
}

.user-notification-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
    border: none !important;
    border-left: none !important;
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
}

.user-notification-error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
    border: none !important;
    border-left: none !important;
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
}

.user-notification-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
    border: none !important;
    border-left: none !important;
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
}

.user-notification-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%) !important;
    border: none !important;
    border-left: none !important;
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
}

/* Override any Bootstrap alert styles that might interfere */
.user-notification,
.user-notification.alert,
.user-notification[class*="alert-"],
.alert.user-notification {
    border: none !important;
    border-left: none !important;
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
    border-radius: 0.75rem !important;
}

/* Ensure all notification types have no borders */
.user-notification-success,
.user-notification-error,
.user-notification-warning,
.user-notification-info,
.user-notification {
    border: none !important;
    border-left: none !important;
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
}

.notification-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--user-spacing);
}

.notification-close {
    background: none;
    border: none;
    font-size: var(--user-font-size-lg);
    cursor: pointer;
    color: var(--user-text-muted);
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== LOADING OVERLAY ===== */
.user-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 9998;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--user-border-color);
    border-top-color: var(--user-primary);
    border-radius: 50%;
    animation: user-spin 1s linear infinite;
    margin: 0 auto var(--user-spacing) auto;
}

.loading-text {
    color: var(--user-text-secondary);
    font-weight: 500;
}

/* ===== LOGIN POPUP ===== */
.login-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: user-fadeIn 0.3s ease-out;
}

.login-popup {
    background: linear-gradient(135deg, var(--user-surface), var(--user-border-light));
    border-radius: var(--user-border-radius-lg);
    padding: var(--user-spacing-xxl);
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: var(--user-shadow-lg);
    border: 1px solid rgba(0, 0, 0, 0.1);
    animation: user-slideIn 0.5s ease-out;
}

.popup-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--user-spacing-lg) auto;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: 700;
}

.popup-logo img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

.popup-title {
    font-size: var(--user-font-size-xxl);
    font-weight: 700;
    color: var(--user-primary);
    margin-bottom: var(--user-spacing);
}

.popup-usdt-bonus {
    font-size: var(--user-font-size-xl);
    font-weight: 600;
    color: var(--user-success);
    margin-bottom: var(--user-spacing-lg);
}

.popup-message {
    color: var(--user-text-secondary);
    line-height: 1.6;
    margin-bottom: var(--user-spacing-xl);
}

.popup-close-btn {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    border: none;
    padding: var(--user-spacing) var(--user-spacing-xl);
    border-radius: var(--user-border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--user-transition);
}

.popup-close-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--user-shadow);
}

/* ===== MARQUEE BANNER ===== */
.marquee-banner {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    padding: var(--user-spacing-sm) 0;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
    margin-bottom: var(--user-spacing);
}

.marquee-content {
    display: inline-block;
    animation: marquee 30s linear infinite;
    padding-left: 100%;
}

.marquee-text {
    font-weight: 500;
    font-size: var(--user-font-size);
}

@keyframes marquee {
    0% { transform: translate3d(100%, 0, 0); }
    100% { transform: translate3d(-100%, 0, 0); }
}

@keyframes user-fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes user-slideOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-20px); }
}

.marquee-banner.paused .marquee-content {
    animation-play-state: paused;
}

.marquee-close {
    position: absolute;
    right: var(--user-spacing);
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: var(--user-transition);
}

.marquee-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ===== USDT NOTIFICATION BANNER ===== */
.usdt-notification-banner {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: var(--user-spacing) 0;
    position: relative;
    margin-bottom: var(--user-spacing-sm);
    border-radius: var(--user-border-radius);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    animation: usdtPulse 2s ease-in-out infinite;
}

.usdt-notification-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--user-spacing);
    padding: 0 var(--user-spacing-xl);
}

.usdt-icon {
    font-size: 1.5rem;
    animation: usdtBounce 1s ease-in-out infinite;
}

.usdt-text {
    font-weight: 600;
    font-size: var(--user-font-size);
    text-align: center;
    line-height: 1.4;
}

.usdt-close {
    position: absolute;
    right: var(--user-spacing);
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: var(--user-transition);
}

.usdt-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
}

@keyframes usdtPulse {
    0%, 100% { box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3); }
    50% { box-shadow: 0 6px 20px rgba(245, 158, 11, 0.5); }
}

@keyframes usdtBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}
