<?php
define('BAMBOO_APP', true);
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Add sample customer service contacts
$contacts = [
    [
        'name' => 'Telegram Support',
        'link' => 'https://t.me/bamboo_support'
    ],
    [
        'name' => 'WhatsApp Support',
        'link' => 'https://wa.me/1234567890'
    ],
    [
        'name' => 'Email Support',
        'link' => 'mailto:<EMAIL>'
    ]
];

foreach ($contacts as $contact) {
    // Check if contact already exists
    $existing = fetchRow("SELECT id FROM customer_service_contacts WHERE name = ?", [$contact['name']]);
    
    if (!$existing) {
        if (insertRecord('customer_service_contacts', $contact)) {
            echo "Added: " . $contact['name'] . "\n";
        } else {
            echo "Failed to add: " . $contact['name'] . "\n";
        }
    } else {
        echo "Already exists: " . $contact['name'] . "\n";
    }
}

echo "Customer service contacts setup complete!\n";
?>
