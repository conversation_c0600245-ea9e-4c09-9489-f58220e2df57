/**
 * Bamboo User Application - Edit Profile Page Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* Page Title */
.edit-profile-page-title {
    background: linear-gradient(135deg, var(--user-primary) 0%, var(--user-gradient-end) 100%);
    color: white;
    padding: var(--user-spacing-xl);
    border-radius: var(--user-border-radius-lg);
    margin-bottom: var(--user-spacing-xl);
    text-align: center;
    margin-left: 0;
    margin-right: 0;
}

.edit-profile-page-title .page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-align: center;
}

.edit-profile-page-title .page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    text-align: center;
    margin: 0;
}

/* Profile Edit Form Card */
.edit-profile-form-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.edit-profile-form-card .user-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.edit-profile-form-card .user-card-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--user-text-primary);
}

.form-status {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    background: #d4edda;
    color: #155724;
}

/* Form Sections */
.edit-profile-form {
    padding: 1.5rem;
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 1rem;
}

/* Avatar Management */
.avatar-management {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.current-avatar {
    flex-shrink: 0;
}

.avatar-preview {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #e9ecef;
    transition: all 0.3s ease;
}

.avatar-preview:hover {
    border-color: var(--user-primary);
    transform: scale(1.05);
}

.avatar-preview-initials {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.5rem;
    border: 3px solid #e9ecef;
    transition: all 0.3s ease;
}

.avatar-preview-initials:hover {
    border-color: var(--user-primary);
    transform: scale(1.05);
}

.avatar-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.avatar-input {
    display: none;
}

.section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--user-text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title::before {
    content: '';
    width: 4px;
    height: 1.2rem;
    background: var(--user-primary);
    border-radius: 2px;
}

.section-description {
    font-size: 0.9rem;
    color: var(--user-text-secondary);
    margin-bottom: 1.5rem;
    font-style: italic;
}

/* Form Groups */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--user-text-primary);
}

.label-text {
    margin-right: 0.25rem;
}

.required {
    color: #dc3545;
    font-weight: 600;
}

/* Form Controls */
.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--user-primary);
    box-shadow: 0 0 0 3px rgba(var(--user-primary-rgb), 0.1);
}

.form-control:invalid {
    border-color: #dc3545;
}

.form-control:valid {
    border-color: #28a745;
}

.form-help {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--user-text-muted);
}

/* Select Styling */
select.form-control {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 0.5rem;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.password-strength.weak .password-strength-bar {
    width: 33%;
    background: #dc3545;
}

.password-strength.medium .password-strength-bar {
    width: 66%;
    background: #ffc107;
}

.password-strength.strong .password-strength-bar {
    width: 100%;
    background: #28a745;
}

/* Form Actions */
.form-actions {
    margin-top: 2rem;
    text-align: center;
}

.user-btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    min-width: 200px;
}

/* Back Link */
.back-link {
    margin-top: 2rem;
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid #dee2e6;
}

/* Validation Feedback */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #28a745;
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

/* Loading State */
.edit-profile-form.loading {
    opacity: 0.7;
    pointer-events: none;
}

.edit-profile-form.loading .user-btn {
    position: relative;
}

.edit-profile-form.loading .user-btn::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .edit-profile-page-title .page-title {
        font-size: 2rem;
    }
    
    .edit-profile-form-card .user-card-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .user-btn-lg {
        min-width: 100%;
    }
    
    .form-section {
        padding-bottom: 1rem;
    }
}

/* Animation */
.user-fade-in {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Security Notice */
.security-notice {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.security-notice-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}
