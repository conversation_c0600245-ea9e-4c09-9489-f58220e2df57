/**
 * Bamboo User Application - Notifications Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

$(document).ready(function() {
    console.log('Notifications page initialized');
    
    // Initialize notifications page functionality
    initializeNotificationsPage();
});

function initializeNotificationsPage() {
    // Handle mark as read buttons
    $('.mark-read-btn').on('click', function(e) {
        e.preventDefault();
        const $form = $(this).closest('form');
        const $notificationItem = $(this).closest('.notification-item');
        
        // Add loading state
        $(this).html('⏳').prop('disabled', true);
        
        // Submit form via AJAX
        $.ajax({
            url: window.location.href,
            method: 'POST',
            data: $form.serialize(),
            success: function(response) {
                // Mark notification as read visually
                $notificationItem.removeClass('unread').addClass('read');
                $notificationItem.find('.unread-indicator').remove();
                
                // Remove the mark as read button
                $form.remove();
                
                // Update unread count
                updateUnreadCount(-1);
                
                // Show success message
                UserApp.showNotification('Notification marked as read', 'success', 2000);
            },
            error: function() {
                // Reset button on error
                $(this).html('✅').prop('disabled', false);
                UserApp.showNotification('Failed to mark notification as read', 'error');
            }
        });
    });
    
    // Handle delete buttons
    $('.delete-btn').on('click', function(e) {
        e.preventDefault();
        const $form = $(this).closest('form');
        const $notificationItem = $(this).closest('.notification-item');
        const isUnread = $notificationItem.hasClass('unread');
        
        if (confirm('Are you sure you want to delete this notification?')) {
            // Add loading state
            $(this).html('⏳').prop('disabled', true);
            
            // Submit form via AJAX
            $.ajax({
                url: window.location.href,
                method: 'POST',
                data: $form.serialize(),
                success: function(response) {
                    // Remove notification with animation
                    $notificationItem.fadeOut(300, function() {
                        $(this).remove();
                        
                        // Check if no notifications left
                        if ($('.notification-item').length === 0) {
                            showNoNotificationsMessage();
                        }
                    });
                    
                    // Update unread count if it was unread
                    if (isUnread) {
                        updateUnreadCount(-1);
                    }
                    
                    // Show success message
                    UserApp.showNotification('Notification deleted', 'success', 2000);
                },
                error: function() {
                    // Reset button on error
                    $(this).html('🗑️').prop('disabled', false);
                    UserApp.showNotification('Failed to delete notification', 'error');
                }
            });
        }
    });
    
    // Handle mark all as read
    $('form input[value="mark_all_read"]').closest('form').on('submit', function(e) {
        e.preventDefault();
        const $form = $(this);
        const $button = $form.find('button[type="submit"]');
        
        // Add loading state
        $button.html('<span class="spinner-border spinner-border-sm me-2"></span>Marking...').prop('disabled', true);
        
        // Submit form via AJAX
        $.ajax({
            url: window.location.href,
            method: 'POST',
            data: $form.serialize(),
            success: function(response) {
                // Mark all notifications as read visually
                $('.notification-item.unread').removeClass('unread').addClass('read');
                $('.unread-indicator').remove();
                $('.mark-read-btn').closest('form').remove();
                
                // Update unread count to 0
                $('.unread-count').text('0');
                
                // Hide the mark all read button
                $form.fadeOut();
                
                // Show success message
                UserApp.showNotification('All notifications marked as read', 'success', 3000);
            },
            error: function() {
                // Reset button on error
                $button.html('✅ Mark All Read').prop('disabled', false);
                UserApp.showNotification('Failed to mark all notifications as read', 'error');
            }
        });
    });
    
    // Add hover effects for notification items
    $('.notification-item').on('mouseenter', function() {
        $(this).addClass('hover-effect');
    }).on('mouseleave', function() {
        $(this).removeClass('hover-effect');
    });
    
    // Auto-refresh notifications every 30 seconds
    setInterval(function() {
        refreshNotifications();
    }, 30000);
    
    // Add animation classes when elements come into view
    observeElements();
}

// Function to update unread count
function updateUnreadCount(change) {
    const $unreadCount = $('.unread-count');
    const currentCount = parseInt($unreadCount.text()) || 0;
    const newCount = Math.max(0, currentCount + change);
    
    $unreadCount.text(newCount);
    
    // Update header unread count if it exists
    if (window.UserApp && typeof UserApp.updateUnreadCount === 'function') {
        UserApp.updateUnreadCount(newCount);
    }
}

// Function to show no notifications message
function showNoNotificationsMessage() {
    const noNotificationsHtml = `
        <div class="no-notifications">
            <div class="no-notifications-icon">🔔</div>
            <h5>No Notifications</h5>
            <p>You don't have any notifications yet. When you receive alerts about your account activities, they will appear here.</p>
        </div>
    `;
    
    $('.notifications-list').html(noNotificationsHtml);
    $('.notifications-actions').fadeOut();
    $('.notifications-pagination').fadeOut();
}

// Function to refresh notifications
function refreshNotifications() {
    // Only refresh if user is active (to avoid unnecessary requests)
    if (document.visibilityState === 'visible') {
        console.log('Refreshing notifications...');
        
        // You can implement AJAX refresh here if needed
        // For now, we'll just log it
    }
}

// Function to observe elements for animations
function observeElements() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    // Observe all fade-in elements
    document.querySelectorAll('.user-fade-in').forEach(el => {
        observer.observe(el);
    });
}

// Function to format notification time
function formatNotificationTime(timestamp) {
    const now = new Date();
    const notificationTime = new Date(timestamp);
    const diffInSeconds = Math.floor((now - notificationTime) / 1000);
    
    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    }
}

// Export functions for global access
window.NotificationsPage = {
    markAsRead: function(notificationId) {
        const $form = $(`.notification-item[data-id="${notificationId}"] .mark-read-btn`).closest('form');
        if ($form.length) {
            $form.find('.mark-read-btn').click();
        }
    },
    
    deleteNotification: function(notificationId) {
        const $form = $(`.notification-item[data-id="${notificationId}"] .delete-btn`).closest('form');
        if ($form.length) {
            $form.find('.delete-btn').click();
        }
    },
    
    markAllAsRead: function() {
        $('form input[value="mark_all_read"]').closest('form').submit();
    }
};
