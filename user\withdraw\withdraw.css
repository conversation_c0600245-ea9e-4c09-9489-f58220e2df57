/**
 * Bamboo User Application - Withdraw Page Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* Page Title */
.withdraw-page-title {
    background: linear-gradient(135deg, var(--user-primary) 0%, var(--user-gradient-end) 100%);
    color: white;
    padding: var(--user-spacing-xl);
    border-radius: var(--user-border-radius-lg);
    margin-bottom: var(--user-spacing-xl);
    text-align: center;
    margin-left: 0;
    margin-right: 0;
}

.withdraw-page-title .page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-align: center;
}

.withdraw-page-title .page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    text-align: center;
    margin: 0;
}

/* Balance Display */
.balance-display {
    margin-bottom: 2rem;
}

.balance-card {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.balance-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.balance-breakdown {
    display: flex;
    justify-content: center;
    gap: 2rem;
    font-size: 0.9rem;
    opacity: 0.8;
}

.balance-breakdown span {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

/* Withdraw Method Notice */
.withdraw-method-notice {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.notice-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.notice-text {
    flex: 1;
    font-size: 1rem;
}

/* Withdrawal Form Card */
.withdraw-form-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.withdraw-form-card .user-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: between;
    align-items: center;
}

.withdraw-form-card .user-card-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--user-text-primary);
}

.form-status {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    background: #d4edda;
    color: #155724;
}

.form-status:contains("Setup Required") {
    background: #f8d7da;
    color: #721c24;
}

/* Setup Required */
.setup-required {
    text-align: center;
    padding: 2rem;
}

.setup-required p {
    font-size: 1.1rem;
    color: #856404;
    margin-bottom: 1.5rem;
}

/* Current Withdrawal Info */
.current-withdrawal-info {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.current-withdrawal-info h6 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--user-text-primary);
    margin-bottom: 1rem;
}

.info-grid {
    display: grid;
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 8px;
}

.info-item label {
    font-weight: 500;
    color: var(--user-text-secondary);
    margin: 0;
}

.info-item span {
    font-weight: 600;
    color: var(--user-text-primary);
    word-break: break-all;
}

/* Form Styles */
.withdraw-form {
    padding: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--user-text-primary);
}

.label-text {
    margin-right: 0.25rem;
}

.required {
    color: #dc3545;
    font-weight: 600;
}

/* Amount Input */
.amount-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.currency-symbol {
    position: absolute;
    left: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--user-text-secondary);
    z-index: 2;
}

.amount-input {
    padding-left: 2.5rem !important;
    font-size: 1.1rem;
    font-weight: 600;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--user-primary);
    box-shadow: 0 0 0 3px rgba(var(--user-primary-rgb), 0.1);
}

.form-control:invalid {
    border-color: #dc3545;
}

.form-control:valid {
    border-color: #28a745;
}

.form-help {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--user-text-muted);
}

/* Form Actions */
.form-actions {
    margin-top: 2rem;
    text-align: center;
}

.user-btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    min-width: 250px;
}

/* Back Link */
.back-link {
    margin-top: 2rem;
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .withdraw-page-title .page-title {
        font-size: 2rem;
    }
    
    .balance-amount {
        font-size: 2rem;
    }
    
    .balance-breakdown {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .user-btn-lg {
        min-width: 100%;
    }
}

/* Animation */
.user-fade-in {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Quick Amount Buttons */
.quick-amounts {
    margin-top: 1rem;
}

.quick-amount-buttons {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

.quick-amount-btn {
    padding: 0.5rem 1rem;
    border: 2px solid var(--user-primary);
    background: white;
    color: var(--user-primary);
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-amount-btn:hover {
    background: var(--user-primary);
    color: white;
    transform: translateY(-1px);
}

/* PIN Input Wrapper */
.pin-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.pin-toggle-btn {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    z-index: 2;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.pin-toggle-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.pin-input-wrapper .form-control {
    padding-right: 3rem;
}

/* Form Error Message */
.form-error-message {
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    font-size: 0.9rem;
}

/* Validation Feedback */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #28a745;
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

/* Loading State */
.withdraw-form.loading {
    opacity: 0.7;
    pointer-events: none;
}

.withdraw-form.loading .user-btn {
    position: relative;
}

.withdraw-form.loading .user-btn::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
