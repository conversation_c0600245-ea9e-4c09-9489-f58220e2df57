<?php
/**
 * Database Analysis Script
 * This script will analyze the database structure and provide comprehensive information
 */

// Include the configuration and database files
require_once 'includes/config.php';
require_once 'includes/database.php';

echo "=== BAMBOO DATABASE ANALYSIS ===\n\n";

try {
    // Test database connection
    $db = getDB();
    echo "✅ Database connection successful!\n";
    echo "Database: " . DB_NAME . "\n";
    echo "Host: " . DB_HOST . ":" . DB_PORT . "\n\n";
    
    // Get all tables
    echo "=== DATABASE TABLES ===\n";
    $stmt = $db->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "❌ No tables found in database!\n\n";
    } else {
        echo "Found " . count($tables) . " tables:\n";
        foreach ($tables as $table) {
            echo "- $table\n";
        }
        echo "\n";
        
        // Analyze each table structure
        echo "=== TABLE STRUCTURES ===\n\n";
        foreach ($tables as $table) {
            echo "📋 TABLE: $table\n";
            echo str_repeat("-", 50) . "\n";
            
            // Get table structure
            $stmt = $db->query("DESCRIBE $table");
            $columns = $stmt->fetchAll();
            
            echo "Columns:\n";
            foreach ($columns as $column) {
                echo sprintf("  %-20s %-15s %-10s %-10s %-10s %s\n", 
                    $column['Field'], 
                    $column['Type'], 
                    $column['Null'], 
                    $column['Key'], 
                    $column['Default'], 
                    $column['Extra']
                );
            }
            
            // Get row count
            $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "Row count: $count\n";
            
            // Show sample data if table has rows
            if ($count > 0 && $count <= 10) {
                echo "Sample data:\n";
                $stmt = $db->query("SELECT * FROM $table LIMIT 5");
                $rows = $stmt->fetchAll();
                if (!empty($rows)) {
                    // Show column headers
                    $headers = array_keys($rows[0]);
                    echo "  " . implode(" | ", $headers) . "\n";
                    echo "  " . str_repeat("-", strlen(implode(" | ", $headers))) . "\n";
                    
                    // Show data rows
                    foreach ($rows as $row) {
                        $values = array_map(function($val) {
                            return $val === null ? 'NULL' : (strlen($val) > 20 ? substr($val, 0, 17) . '...' : $val);
                        }, array_values($row));
                        echo "  " . implode(" | ", $values) . "\n";
                    }
                }
            } elseif ($count > 10) {
                echo "Sample data (first 3 rows):\n";
                $stmt = $db->query("SELECT * FROM $table LIMIT 3");
                $rows = $stmt->fetchAll();
                if (!empty($rows)) {
                    // Show column headers
                    $headers = array_keys($rows[0]);
                    echo "  " . implode(" | ", $headers) . "\n";
                    echo "  " . str_repeat("-", strlen(implode(" | ", $headers))) . "\n";
                    
                    // Show data rows
                    foreach ($rows as $row) {
                        $values = array_map(function($val) {
                            return $val === null ? 'NULL' : (strlen($val) > 20 ? substr($val, 0, 17) . '...' : $val);
                        }, array_values($row));
                        echo "  " . implode(" | ", $values) . "\n";
                    }
                }
            }
            
            echo "\n";
        }
        
        // Show foreign key relationships
        echo "=== FOREIGN KEY RELATIONSHIPS ===\n";
        $stmt = $db->query("
            SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM 
                INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE 
                REFERENCED_TABLE_SCHEMA = '" . DB_NAME . "'
                AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $foreignKeys = $stmt->fetchAll();
        
        if (empty($foreignKeys)) {
            echo "No foreign key relationships found.\n\n";
        } else {
            foreach ($foreignKeys as $fk) {
                echo sprintf("🔗 %s.%s -> %s.%s (constraint: %s)\n",
                    $fk['TABLE_NAME'],
                    $fk['COLUMN_NAME'],
                    $fk['REFERENCED_TABLE_NAME'],
                    $fk['REFERENCED_COLUMN_NAME'],
                    $fk['CONSTRAINT_NAME']
                );
            }
            echo "\n";
        }
        
        // Show indexes
        echo "=== TABLE INDEXES ===\n";
        foreach ($tables as $table) {
            $stmt = $db->query("SHOW INDEX FROM $table");
            $indexes = $stmt->fetchAll();
            
            if (!empty($indexes)) {
                echo "📊 Indexes for $table:\n";
                $currentIndex = '';
                foreach ($indexes as $index) {
                    if ($index['Key_name'] !== $currentIndex) {
                        $currentIndex = $index['Key_name'];
                        $unique = $index['Non_unique'] == 0 ? 'UNIQUE' : '';
                        echo "  - {$index['Key_name']} $unique\n";
                    }
                }
                echo "\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "=== ANALYSIS COMPLETE ===\n";
?>