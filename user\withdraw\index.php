<?php
/**
 * Bamboo User Application - Withdraw Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: User withdrawal functionality
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Get user balance (stored directly in users table)
$user_balance = [
    'balance' => $current_user['balance'] ?? 0,
    'commission_balance' => $current_user['commission_balance'] ?? 0,
    'total_commission_earned' => $current_user['total_commission_earned'] ?? 0
];

// Calculate total balance
$total_balance = $user_balance['balance'] + $user_balance['commission_balance'];

// Handle withdrawal form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        $amount = floatval($_POST['amount'] ?? 0);
        $withdrawal_pin = trim($_POST['withdrawal_pin'] ?? '');

        // Validation
        $errors = [];
        
        if ($amount <= 0) {
            $errors[] = 'Please enter a valid withdrawal amount.';
        } elseif ($amount > $total_balance) {
            $errors[] = 'Insufficient balance. Your available balance is $' . number_format($total_balance, 2);
        }
        
        if (empty($withdrawal_pin)) {
            $errors[] = 'Withdrawal PIN is required.';
        } else {
            // Verify withdrawal PIN
            if (empty($current_user['withdrawal_pin_hash']) || !password_verify($withdrawal_pin, $current_user['withdrawal_pin_hash'])) {
                $errors[] = 'Invalid withdrawal PIN.';
            }
        }
        
        // Check if user has withdrawal information set
        if (empty($current_user['usdt_wallet_address'])) {
            $errors[] = 'Please set your withdrawal information first in your profile.';
        }
        
        if (empty($errors)) {
            try {
                // Generate unique transaction ID for withdrawal
                $transaction_id = generateTransactionId('WTH');

                // Create withdrawal transaction
                $transaction_data = [
                    'user_id' => $user_id,
                    'type' => 'withdrawal',
                    'amount' => $amount,
                    'status' => 'pending',
                    'transaction_id' => $transaction_id,
                    'description' => 'Withdrawal request',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                if (insertRecord('transactions', $transaction_data)) {
                    // Update user balance (deduct from balance)
                    $remaining_amount = $amount;
                    $new_balance = $user_balance['balance'];
                    $new_commission_balance = $user_balance['commission_balance'];

                    // First deduct from commission balance, then from main balance
                    if ($remaining_amount > 0 && $new_commission_balance > 0) {
                        $commission_deduction = min($remaining_amount, $new_commission_balance);
                        $new_commission_balance -= $commission_deduction;
                        $remaining_amount -= $commission_deduction;
                    }

                    if ($remaining_amount > 0) {
                        $new_balance -= $remaining_amount;
                    }

                    // Update balances in users table
                    updateRecord('users', [
                        'balance' => $new_balance,
                        'commission_balance' => $new_commission_balance
                    ], 'id = ?', [$user_id]);
                    
                    showSuccess('Withdrawal request submitted successfully! You will receive your funds within an hour.');
                    
                    // Refresh balance data
                    $current_user = getCurrentUser();
                    $user_balance = [
                        'balance' => $current_user['balance'] ?? 0,
                        'commission_balance' => $current_user['commission_balance'] ?? 0
                    ];
                    $total_balance = $user_balance['balance'] + $user_balance['commission_balance'];
                } else {
                    showError('Failed to submit withdrawal request. Please try again.');
                }
            } catch (Exception $e) {
                error_log("Withdrawal error: " . $e->getMessage());
                showError('An error occurred while processing your withdrawal. Please try again.');
            }
        } else {
            foreach ($errors as $error) {
                showError($error);
            }
        }
    }
}

// Set page title and description
$page_title = 'Withdraw';
$page_description = 'Withdraw your funds securely';

// Override CSS and JS paths for this directory
$additional_css = [BASE_URL . 'user/withdraw/withdraw.css'];
$additional_js = [BASE_URL . 'user/withdraw/withdraw.js'];

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Withdraw Page Title -->
    <div class="withdraw-page-title user-fade-in">
        <div class="user-container">
            <h1 class="page-title">💸 Withdraw</h1>
            <p class="page-subtitle">You will receive your withdraw within an hour</p>
        </div>
    </div>

    <!-- Total Balance Display -->
    <div class="balance-display user-fade-in">
        <div class="balance-card">
            <h3>Total Balance</h3>
            <div class="balance-amount">USDT $ <?php echo number_format($total_balance, 2); ?></div>
            <div class="balance-breakdown">
                <span>Main: $<?php echo number_format($user_balance['balance'], 2); ?></span>
                <span>Available Commission: $<?php echo number_format($user_balance['commission_balance'], 2); ?></span>
                <span>Total Commission Earned: $<?php echo number_format($user_balance['total_commission_earned'], 2); ?></span>
            </div>
        </div>
    </div>

    <!-- Withdraw Method Notice -->
    <div class="withdraw-method-notice user-fade-in">
        <div class="notice-icon">🏦</div>
        <div class="notice-text">
            <strong>Withdraw Method:</strong> Withdrawal will be transferred to Exchange Wallet
        </div>
    </div>

    <!-- Withdrawal Form -->
    <div class="user-card withdraw-form-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Withdrawal Request</h5>
            <span class="form-status">
                <?php echo (!empty($current_user['usdt_wallet_address'])) ? '✅ Ready' : '⚠️ Setup Required'; ?>
            </span>
        </div>
        <div class="user-card-body">
            <?php if (empty($current_user['usdt_wallet_address'])): ?>
                <div class="setup-required">
                    <p>⚠️ Please set up your withdrawal information first.</p>
                    <a href="<?php echo BASE_URL; ?>user/profile/withdrawal-info/" class="user-btn user-btn-primary">
                        Set Up Withdrawal Info
                    </a>
                </div>
            <?php else: ?>
                <!-- Current Withdrawal Information -->
                <div class="current-withdrawal-info">
                    <h6>Withdrawal Destination</h6>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>TRC20 Address:</label>
                            <span><?php echo htmlspecialchars($current_user['usdt_wallet_address']); ?></span>
                        </div>
                        <div class="info-item">
                            <label>Exchange Name:</label>
                            <span><?php echo htmlspecialchars($current_user['exchange_name'] ?? 'Not set'); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Withdrawal Form -->
                <form method="POST" class="withdraw-form" id="withdrawForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <!-- Amount -->
                    <div class="form-group">
                        <label for="amount" class="form-label">
                            <span class="label-text">Amount</span>
                            <span class="required">*</span>
                        </label>
                        <div class="amount-input-wrapper">
                            <span class="currency-symbol">$</span>
                            <input type="number"
                                   id="amount"
                                   name="amount"
                                   class="form-control amount-input"
                                   placeholder="0.00"
                                   step="0.01"
                                   min="0.01"
                                   max="<?php echo $total_balance; ?>"
                                   required>
                        </div>
                        <small class="form-help">Type withdraw Amount (Max: $<?php echo number_format($total_balance, 2); ?>)</small>
                    </div>

                    <!-- Withdrawal PIN -->
                    <div class="form-group">
                        <label for="withdrawal_pin" class="form-label">
                            <span class="label-text">Withdrawal PIN</span>
                            <span class="required">*</span>
                        </label>
                        <input type="password"
                               id="withdrawal_pin"
                               name="withdrawal_pin"
                               class="form-control"
                               placeholder="Type withdraw Password"
                               required
                               minlength="4">
                        <small class="form-help">Enter your withdrawal PIN for security verification</small>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-actions">
                        <button type="submit" class="user-btn user-btn-primary user-btn-lg">
                            💸 Submit Withdrawal Request
                        </button>
                    </div>
                </form>
            <?php endif; ?>

            <!-- Back to Profile Link -->
            <div class="back-link">
                <a href="<?php echo BASE_URL; ?>user/profile/" class="user-btn user-btn-outline">
                    ← Back to Profile
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Notifications -->
<?php if (isset($_SESSION['success_message'])): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof UserApp !== 'undefined' && UserApp.showNotification) {
                UserApp.showNotification('<?php echo addslashes($_SESSION['success_message']); ?>', 'success');
            }
        });
    </script>
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof UserApp !== 'undefined' && UserApp.showNotification) {
                UserApp.showNotification('<?php echo addslashes($_SESSION['error_message']); ?>', 'error');
            }
        });
    </script>
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

<?php include '../includes/user_footer.php'; ?>
