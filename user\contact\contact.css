/**
 * Bamboo User Application - Contact Page Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* Hero Section */
.contact-hero {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    padding: 3rem 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.hero-content {
    max-width: 600px;
    margin: 0 auto;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-description {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .contact-hero {
        padding: 2rem 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }
}

/* Contact Information */
.contact-info {
    margin-bottom: var(--user-spacing-lg);
}

.contact-methods {
    display: grid;
    gap: 2rem;
    margin-bottom: 2rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid var(--user-border-color);
    border-radius: var(--user-border-radius);
    transition: all 0.3s ease;
    background: white;
}

.contact-method:hover {
    border-color: var(--user-primary);
    background: var(--user-bg-light);
    transform: translateY(-2px);
}

.method-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--user-primary), var(--user-primary-dark));
    border-radius: var(--user-border-radius);
    color: white;
    flex-shrink: 0;
}

.method-info h6 {
    font-size: var(--user-font-size-lg);
    font-weight: 600;
    margin-bottom: var(--user-spacing-xs);
    color: var(--user-text-primary);
}

.method-info p {
    color: var(--user-text-secondary);
    margin-bottom: var(--user-spacing-sm);
    line-height: 1.5;
}

/* Support Hours */
.support-hours {
    margin-bottom: var(--user-spacing-lg);
}

.hours-grid {
    display: grid;
    gap: var(--user-spacing-sm);
    margin-bottom: var(--user-spacing-md);
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--user-spacing-sm) var(--user-spacing-md);
    background: var(--user-bg-light);
    border-radius: var(--user-border-radius);
    border-left: 4px solid var(--user-primary);
}

.hours-item .day {
    font-weight: 600;
    color: var(--user-text-primary);
}

.hours-item .time {
    color: var(--user-text-secondary);
    font-weight: 500;
}

.hours-note {
    padding: var(--user-spacing-sm) var(--user-spacing-md);
    background: var(--user-info-bg);
    border: 1px solid var(--user-info-border);
    border-radius: var(--user-border-radius);
    color: var(--user-info-text);
}

.hours-note p {
    margin: 0;
    font-size: var(--user-font-size-sm);
}

/* Customer Care Modal */
.modal-header {
    border-bottom: 1px solid var(--user-border-color);
    background: var(--user-bg-light);
}

.modal-title {
    display: flex;
    align-items: center;
    gap: var(--user-spacing-sm);
    font-weight: 600;
    color: var(--user-text-primary);
}

.modal-icon {
    font-size: 1.2rem;
}

.customer-care-intro {
    margin-bottom: var(--user-spacing-md);
    padding: var(--user-spacing-sm) var(--user-spacing-md);
    background: var(--user-bg-light);
    border-radius: var(--user-border-radius);
    color: var(--user-text-secondary);
}

.customer-care-intro p {
    margin: 0;
    line-height: 1.5;
}

/* Customer Care List */
.customer-care-list {
    display: flex;
    flex-direction: column;
    gap: var(--user-spacing-sm);
}

.care-contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    border: 1px solid var(--user-border-color);
    border-radius: var(--user-border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.care-contact-item:hover {
    border-color: var(--user-primary);
    background: var(--user-bg-light);
    transform: translateX(5px);
}

.care-contact-item .contact-icon {
    font-size: 1.2rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--user-primary), var(--user-primary-dark));
    border-radius: var(--user-border-radius);
    color: white;
    flex-shrink: 0;
}

.care-contact-item .contact-details {
    flex: 1;
}

.care-contact-item .contact-name {
    font-size: var(--user-font-size-md);
    font-weight: 600;
    margin-bottom: var(--user-spacing-xs);
    color: var(--user-text-primary);
}

.care-contact-item .contact-type {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-secondary);
    margin: 0;
    text-transform: capitalize;
}

.care-contact-item .contact-action {
    color: var(--user-primary);
    font-size: 1.2rem;
    font-weight: 600;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.care-contact-item:hover .contact-action {
    opacity: 1;
}

/* No Contacts State */
.no-contacts {
    text-align: center;
    padding: var(--user-spacing-xl);
    color: var(--user-text-secondary);
}

.no-contacts-icon {
    font-size: 3rem;
    margin-bottom: var(--user-spacing-md);
    opacity: 0.5;
}

.no-contacts h6 {
    font-size: var(--user-font-size-lg);
    font-weight: 600;
    margin-bottom: var(--user-spacing-sm);
    color: var(--user-text-primary);
}

.no-contacts p {
    margin: 0;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-hero {
        padding: var(--user-spacing-lg) 0;
        margin-bottom: var(--user-spacing-md);
    }
    
    .hero-title {
        font-size: var(--user-font-size-xl);
    }
    
    .hero-description {
        font-size: var(--user-font-size-md);
    }
    
    .contact-method {
        flex-direction: column;
        text-align: center;
        gap: var(--user-spacing-sm);
    }
    
    .method-icon {
        margin: 0 auto;
    }
    
    .hours-item {
        flex-direction: column;
        gap: var(--user-spacing-xs);
        text-align: center;
    }
    
    .care-contact-item {
        padding: var(--user-spacing-sm);
    }
    
    .care-contact-item .contact-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
}
