$(document).ready(function() {
    // Add a loading indicator to all forms (but allow form to submit)
    $('form').on('submit', function(e) {
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');

        // Only add loading state if form is valid and not already processing
        if (!$submitBtn.prop('disabled')) {
            // Store original button content
            const originalContent = $submitBtn.html();
            $submitBtn.data('original-content', originalContent);

            // Add loading state after a short delay to ensure form submits
            setTimeout(function() {
                if (!$submitBtn.prop('disabled')) {
                    $submitBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...');
                }
            }, 100);
        }
    });

    // Sidebar scroll position preservation
    const SIDEBAR_SCROLL_KEY = 'admin_sidebar_scroll_position';
    const sidebar = $('.admin-sidebar');

    // Restore scroll position on page load
    if (sidebar.length) {
        const savedScrollPosition = sessionStorage.getItem(SIDEBAR_SCROLL_KEY);
        if (savedScrollPosition) {
            sidebar.scrollTop(parseInt(savedScrollPosition, 10));
        }

        // Save scroll position when scrolling
        sidebar.on('scroll', function() {
            sessionStorage.setItem(SIDEBAR_SCROLL_KEY, $(this).scrollTop());
        });

        // Save scroll position before navigation
        $('.admin-sidebar .nav-link').on('click', function() {
            sessionStorage.setItem(SIDEBAR_SCROLL_KEY, sidebar.scrollTop());
        });

        // Also save on window beforeunload (for browser navigation)
        $(window).on('beforeunload', function() {
            sessionStorage.setItem(SIDEBAR_SCROLL_KEY, sidebar.scrollTop());
        });
    }
});
