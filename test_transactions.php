<?php
/**
 * Test script for transactions functionality
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Start session
session_start();

echo "<h1>Transactions Test</h1>";

// Test database connection
try {
    $pdo = getDbConnection();
    echo "<p>✅ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

// Test if transactions table exists
try {
    $result = fetchRow("SELECT COUNT(*) as count FROM transactions LIMIT 1");
    echo "<p>✅ Transactions table exists with " . $result['count'] . " records</p>";
} catch (Exception $e) {
    echo "<p>❌ Transactions table error: " . $e->getMessage() . "</p>";
}

// Test required functions
$functions_to_test = [
    'formatCurrency',
    'getUserBalance', 
    'getUserVipLevel',
    'getRecentTransactions',
    'isLoggedIn',
    'getCurrentUser'
];

foreach ($functions_to_test as $function) {
    if (function_exists($function)) {
        echo "<p>✅ Function $function exists</p>";
    } else {
        echo "<p>❌ Function $function missing</p>";
    }
}

// Test formatCurrency function
try {
    $formatted = formatCurrency(1234.56);
    echo "<p>✅ formatCurrency test: $formatted</p>";
} catch (Exception $e) {
    echo "<p>❌ formatCurrency error: " . $e->getMessage() . "</p>";
}

// Test if user is logged in
if (isLoggedIn()) {
    echo "<p>✅ User is logged in</p>";
    
    $user = getCurrentUser();
    echo "<p>Current user: " . htmlspecialchars($user['username']) . "</p>";
    
    // Test user functions
    try {
        $balance = getUserBalance($user['id']);
        echo "<p>✅ User balance: " . formatCurrency($balance['balance']) . "</p>";
    } catch (Exception $e) {
        echo "<p>❌ getUserBalance error: " . $e->getMessage() . "</p>";
    }
    
    try {
        $vip = getUserVipLevel($user['id']);
        echo "<p>✅ User VIP level: " . ($vip['level'] ?? 'N/A') . "</p>";
    } catch (Exception $e) {
        echo "<p>❌ getUserVipLevel error: " . $e->getMessage() . "</p>";
    }
    
    try {
        $transactions = getRecentTransactions($user['id'], 5);
        echo "<p>✅ Recent transactions: " . count($transactions) . " found</p>";
    } catch (Exception $e) {
        echo "<p>❌ getRecentTransactions error: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p>⚠️ User is not logged in - need to login to test user functions</p>";
    echo "<p><a href='user/login/'>Login here</a></p>";
}

echo "<h2>Test Complete</h2>";
echo "<p><a href='user/transactions/'>Go to Transactions Page</a></p>";
?>