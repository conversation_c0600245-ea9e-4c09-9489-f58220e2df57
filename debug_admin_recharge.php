<?php
/**
 * Debug Admin Recharge Functionality
 */

define('BAMBOO_APP', true);
require_once 'includes/config.php';
require_once 'includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h2>Admin Recharge Debug Test</h2>";

// Test 1: Check admin login status
echo "<h3>1. Admin Login Status</h3>";
if (isAdminLoggedIn()) {
    echo "✅ Admin is logged in<br>";
    echo "Admin ID: " . ($_SESSION['admin_id'] ?? 'Not set') . "<br>";
} else {
    echo "❌ Admin is NOT logged in<br>";
    echo "Session data: " . print_r($_SESSION, true) . "<br>";
}

// Test 2: Check CSRF token generation
echo "<h3>2. CSRF Token Test</h3>";
try {
    $csrf_token = generateCSRFToken();
    echo "✅ CSRF Token generated: " . substr($csrf_token, 0, 20) . "...<br>";
    
    // Test verification
    if (verifyCSRFToken($csrf_token)) {
        echo "✅ CSRF Token verification works<br>";
    } else {
        echo "❌ CSRF Token verification failed<br>";
    }
} catch (Exception $e) {
    echo "❌ CSRF Token error: " . $e->getMessage() . "<br>";
}

// Test 3: Check database connection
echo "<h3>3. Database Connection Test</h3>";
try {
    $test_query = fetchRow("SELECT COUNT(*) as count FROM transactions WHERE type = 'deposit' AND status = 'pending'");
    echo "✅ Database connection works<br>";
    echo "Pending deposits: " . $test_query['count'] . "<br>";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 4: Check recent pending transactions
echo "<h3>4. Recent Pending Transactions</h3>";
try {
    $pending_transactions = fetchAll("
        SELECT t.id, t.amount, t.status, t.created_at, u.username 
        FROM transactions t 
        LEFT JOIN users u ON t.user_id = u.id 
        WHERE t.type = 'deposit' AND t.status = 'pending' 
        ORDER BY t.created_at DESC 
        LIMIT 5
    ");
    
    if (!empty($pending_transactions)) {
        echo "✅ Found " . count($pending_transactions) . " pending transactions:<br>";
        foreach ($pending_transactions as $txn) {
            echo "- ID: {$txn['id']}, User: {$txn['username']}, Amount: {$txn['amount']}, Date: {$txn['created_at']}<br>";
        }
    } else {
        echo "ℹ️ No pending transactions found<br>";
    }
} catch (Exception $e) {
    echo "❌ Error fetching transactions: " . $e->getMessage() . "<br>";
}

// Test 5: Simulate approve action
echo "<h3>5. Simulate Approve Action</h3>";
if (!empty($pending_transactions)) {
    $test_transaction = $pending_transactions[0];
    echo "Testing with transaction ID: " . $test_transaction['id'] . "<br>";
    
    // Simulate the approval process
    try {
        $transaction = fetchRow('SELECT * FROM transactions WHERE id = ?', [$test_transaction['id']]);
        if ($transaction && $transaction['status'] === 'pending') {
            echo "✅ Transaction found and is pending<br>";
            
            // Check user exists
            $user = fetchRow('SELECT id, balance FROM users WHERE id = ?', [$transaction['user_id']]);
            if ($user) {
                echo "✅ User found, current balance: " . $user['balance'] . "<br>";
                echo "ℹ️ Would add: " . $transaction['amount'] . "<br>";
                echo "ℹ️ New balance would be: " . ($user['balance'] + $transaction['amount']) . "<br>";
                echo "✅ Approval simulation successful (not actually executed)<br>";
            } else {
                echo "❌ User not found for transaction<br>";
            }
        } else {
            echo "❌ Transaction not found or not pending<br>";
        }
    } catch (Exception $e) {
        echo "❌ Approval simulation error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "ℹ️ No transactions to test with<br>";
}

// Test 6: Check form submission simulation
echo "<h3>6. Form Submission Test</h3>";
echo "Testing POST data handling...<br>";

// Simulate POST data
$_POST['csrf_token'] = $csrf_token;
$_POST['action'] = 'approve';
$_POST['transaction_id'] = $pending_transactions[0]['id'] ?? 1;

echo "Simulated POST data:<br>";
echo "- CSRF Token: " . substr($_POST['csrf_token'], 0, 20) . "...<br>";
echo "- Action: " . $_POST['action'] . "<br>";
echo "- Transaction ID: " . $_POST['transaction_id'] . "<br>";

// Test CSRF verification with POST data
if (verifyCSRFToken($_POST['csrf_token'])) {
    echo "✅ CSRF verification would pass<br>";
} else {
    echo "❌ CSRF verification would fail<br>";
}

echo "<h3>7. Recommendations</h3>";
if (!isAdminLoggedIn()) {
    echo "❌ <strong>Issue Found:</strong> Admin is not logged in. Please log in to admin panel first.<br>";
}

echo "<br><strong>Next Steps:</strong><br>";
echo "1. Ensure admin is logged in<br>";
echo "2. Check browser console for JavaScript errors<br>";
echo "3. Verify forms are submitting correctly<br>";
echo "4. Check server error logs<br>";

?>