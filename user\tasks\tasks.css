/**
 * Bamboo User Dashboard - Tasks Page Styles
 * Company: Notepadsly
 * Version: 1.0
 * Description: Flagship task submission page styling
 */

/* ===== TASK PAGE TITLE ===== */
.task-page-title {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    padding: var(--user-spacing-xl) 0;
    border-radius: var(--user-border-radius-lg);
    margin-bottom: var(--user-spacing-xl);
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 var(--user-spacing-sm) 0;
    text-align: center;
}

.page-subtitle {
    font-size: var(--user-font-size-lg);
    opacity: 0.9;
    text-align: center;
    margin: 0;
}

/* ===== TASK STATISTICS ===== */
.task-stats-container {
    margin-bottom: var(--user-spacing-xl);
}

/* Fix spacing between stat cards */
.task-stats-container .user-row {
    margin-left: 0;
    margin-right: 0;
}

.task-stats-container .user-col-6 {
    padding-left: calc(var(--user-spacing-sm) / 2);
    padding-right: calc(var(--user-spacing-sm) / 2);
}

.task-stats-container .user-col-6:first-child {
    padding-left: 0;
}

.task-stats-container .user-col-6:last-child {
    padding-right: 0;
}

.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.9));
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--user-border-radius-lg);
    padding: var(--user-spacing-xl);
    text-align: center;
    box-shadow: var(--user-shadow);
    transition: var(--user-transition);
    margin-bottom: 0; /* Remove default margin since we're using column spacing */
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--user-shadow-lg);
}

.profit-card {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.05));
    border-color: rgba(40, 167, 69, 0.2);
}

.balance-card {
    background: linear-gradient(135deg, rgba(255, 105, 0, 0.1), rgba(255, 133, 51, 0.05));
    border-color: rgba(255, 105, 0, 0.2);
}

.stat-label {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
    margin-bottom: var(--user-spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--user-primary);
    margin-bottom: var(--user-spacing-xs);
}

.stat-note {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-secondary);
    font-family: 'Inter', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-weight: 400;
}

/* ===== TASK PROGRESS ===== */
.task-progress-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--user-border-radius);
    padding: var(--user-spacing-lg);
    margin-top: var(--user-spacing-xl);
    margin-bottom: var(--user-spacing-xl);
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--user-spacing);
}

.progress-label {
    font-weight: 600;
    color: var(--user-text-primary);
}

.progress-count {
    font-weight: 700;
    font-size: var(--user-font-size-lg);
    color: var(--user-primary);
}

.progress-bar-container {
    width: 100%;
    height: 8px;
    background-color: var(--user-border-light);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    transition: width 0.5s ease;
}

/* ===== PRODUCT GRID ===== */
.product-grid {
    margin-bottom: var(--user-spacing-xl);
}

.grid-title {
    text-align: center;
    margin-bottom: var(--user-spacing-lg);
    color: var(--user-text-primary);
    font-weight: 600;
    font-size: 1.25rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.products-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: var(--user-spacing-xl);
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--user-border-radius-lg);
    backdrop-filter: blur(10px);
    /* Display 9 products in 3x3 grid with better spacing */
    grid-template-rows: repeat(3, 1fr);
    min-height: auto;
    max-width: 600px;
    margin: 0 auto var(--user-spacing-xl) auto;
}

.product-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: var(--user-border-radius);
    overflow: hidden;
    cursor: pointer;
    transition: var(--user-transition);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.9));
    border: 2px solid rgba(255, 105, 0, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    /* Enhanced size for better 3x3 grid layout */
    height: 140px;
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

/* Staggered animation for grid items */
.product-item:nth-child(1) { animation-delay: 0.1s; }
.product-item:nth-child(2) { animation-delay: 0.2s; }
.product-item:nth-child(3) { animation-delay: 0.3s; }
.product-item:nth-child(4) { animation-delay: 0.4s; }
.product-item:nth-child(5) { animation-delay: 0.5s; }
.product-item:nth-child(6) { animation-delay: 0.6s; }
.product-item:nth-child(7) { animation-delay: 0.7s; }
.product-item:nth-child(8) { animation-delay: 0.8s; }
.product-item:nth-child(9) { animation-delay: 0.9s; }

.product-item:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(255, 105, 0, 0.2);
    border-color: rgba(255, 105, 0, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(255, 248, 240, 0.95));
}

.product-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--user-border-radius);
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--user-border-radius);
}

.product-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--user-border-color), var(--user-border-light));
    border-radius: var(--user-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--user-text-muted);
    font-size: 2rem;
    opacity: 0.7;
}

.product-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));
    color: white;
    padding: 0.5rem;
    text-align: center;
    backdrop-filter: blur(4px);
}

.product-name {
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    margin: 0;
}

/* ===== ACTIVE TASK ===== */
.active-task-container {
    margin-bottom: var(--user-spacing-xl);
}

.active-task-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.9));
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--user-border-radius-lg);
    padding: var(--user-spacing-xl);
    box-shadow: var(--user-shadow-lg);
}

.task-product-image {
    text-align: center;
    margin-bottom: var(--user-spacing-xl);
}

.task-product-image img {
    max-width: 200px;
    max-height: 200px;
    border-radius: var(--user-border-radius);
    box-shadow: var(--user-shadow);
}

.task-product-image .product-placeholder {
    width: 200px;
    height: 200px;
    margin: 0 auto;
    font-size: 3rem;
}

.task-details h3 {
    text-align: center;
    margin-bottom: var(--user-spacing-xl);
    color: var(--user-text-primary);
    font-size: var(--user-font-size-xxl);
}

.task-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--user-spacing-lg);
    margin-bottom: var(--user-spacing-xl);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--user-spacing);
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--user-border-radius);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.info-label {
    font-weight: 500;
    color: var(--user-text-secondary);
}

.info-value {
    font-weight: 600;
    color: var(--user-text-primary);
}

.profit-highlight {
    color: var(--user-success) !important;
    font-size: var(--user-font-size-lg);
}

.task-actions {
    display: flex;
    gap: var(--user-spacing);
    justify-content: center;
}

.task-actions .user-btn {
    min-width: 150px;
}

/* ===== MATCHING CONTROLS ===== */
.matching-controls {
    text-align: center;
    margin-bottom: var(--user-spacing-xl);
}

.start-matching-btn {
    min-width: 200px;
    font-size: var(--user-font-size-lg);
    padding: var(--user-spacing-lg) var(--user-spacing-xxl);
    margin-bottom: var(--user-spacing-lg);
}

.warning-message,
.info-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--user-spacing-sm);
    padding: var(--user-spacing);
    border-radius: var(--user-border-radius);
    margin-top: var(--user-spacing);
    font-size: var(--user-font-size-sm);
}

.warning-message {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--user-danger);
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.info-message {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--user-info);
    border: 1px solid rgba(23, 162, 184, 0.2);
}



/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .products-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        padding: 0.75rem;
        max-width: 400px;
    }

    .product-item {
        height: 120px;
        padding: 0.5rem;
    }

    .task-info-grid {
        grid-template-columns: 1fr;
    }

    .task-actions {
        flex-direction: column;
    }

    .task-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .products-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
        padding: 0.5rem;
        max-width: 320px;
    }

    .product-item {
        height: 100px;
        padding: 0.25rem;
    }

    .product-name {
        font-size: 0.65rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }
}

/* ===== MATCHING ANIMATIONS ===== */
.product-item.matching-highlight {
    animation: matchingPulse 0.5s ease-in-out;
    border-color: var(--user-primary);
    box-shadow: 0 0 20px rgba(255, 105, 0, 0.3);
}

.product-item.selected {
    border-color: var(--user-primary);
    box-shadow: 0 0 15px rgba(255, 105, 0, 0.2);
    transform: scale(1.02);
}

@keyframes matchingPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== SUBMISSION SUCCESS OVERLAY ===== */
.submission-success-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: user-fadeIn 0.3s ease-out;
}

.success-content {
    background: linear-gradient(135deg, var(--user-surface), var(--user-border-light));
    border-radius: var(--user-border-radius-lg);
    padding: var(--user-spacing-xxl);
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: var(--user-shadow-lg);
    border: 1px solid rgba(0, 0, 0, 0.1);
    animation: user-slideIn 0.5s ease-out;
}

.success-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--user-spacing-lg) auto;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--user-success), #20c997);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    animation: successBounce 0.6s ease-out;
}

.success-content h3 {
    font-size: var(--user-font-size-xxl);
    font-weight: 700;
    color: var(--user-success);
    margin-bottom: var(--user-spacing-lg);
}

.profit-earned,
.new-balance {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--user-spacing);
    margin-bottom: var(--user-spacing-sm);
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--user-border-radius);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.profit-label,
.balance-label {
    font-weight: 500;
    color: var(--user-text-secondary);
}

.profit-amount {
    font-weight: 700;
    font-size: var(--user-font-size-lg);
    color: var(--user-success);
}

.balance-amount {
    font-weight: 700;
    font-size: var(--user-font-size-lg);
    color: var(--user-primary);
}

@keyframes successBounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* ===== LOADING STATES ===== */
.user-btn.user-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.user-btn.user-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: var(--user-spacing);
    width: 16px;
    height: 16px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: user-spin 1s linear infinite;
}

/* ===== SIMPLE MODAL STYLES ===== */
.simple-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    min-width: 300px;
    max-width: 500px;
    width: 90%;
}

.modal-header {
    padding: 20px 20px 10px 20px;
    border-bottom: 1px solid #eee;
}

.modal-header h4 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.modal-body {
    padding: 20px;
}

.modal-body p {
    margin: 0;
    color: #666;
    line-height: 1.5;
}

.modal-footer {
    padding: 10px 20px 20px 20px;
    text-align: right;
}

.modal-footer .user-btn {
    margin-left: 10px;
}

/* Mobile responsive for modal */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 20px;
    }
}

/* ===== ENHANCED TASK CARD STYLES ===== */
.assigned-task-container {
    background: white;
    border-radius: 20px;
    padding: 30px;
    margin: 20px 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    animation: slideInUp 0.5s ease-out;
}

.task-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.task-image {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    overflow: hidden;
    margin-right: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.task-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.task-image-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: #6c757d;
}

.task-info h3 {
    margin: 0;
    font-size: 1.4em;
    color: #333;
    font-weight: 600;
}

.task-info .task-id {
    margin: 5px 0 0 0;
    color: #666;
    font-size: 0.9em;
}

.task-info .task-category {
    margin: 5px 0 0 0;
    color: #666;
    font-size: 0.9em;
    font-family: 'Inter', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.task-details-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
}

.task-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.task-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.task-detail-label {
    color: #666;
    font-size: 0.9em;
}

.task-detail-value {
    font-weight: 600;
    color: #333;
}

.task-detail-value.commission {
    color: #28a745;
}

.task-description {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    border-left: 4px solid #007bff;
}

.task-description h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1em;
}

.task-description p {
    margin: 0;
    color: #666;
    line-height: 1.5;
    font-family: 'Inter', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.task-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.task-actions button {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    max-width: 150px;
}

.submit-task-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.submit-task-btn:hover {
    background: linear-gradient(135deg, #218838, #1e7e34);
    transform: translateY(-1px);
}

.close-task-btn {
    background: #6c757d;
    color: white;
}

.close-task-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced task card responsive design */
@media (max-width: 768px) {
    .assigned-task-container {
        padding: 20px;
        margin: 15px 0;
    }

    .task-header {
        flex-direction: column;
        text-align: center;
    }

    .task-image {
        margin: 0 0 15px 0;
    }

    .task-details-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .task-actions {
        flex-direction: column;
    }

    .task-actions button {
        max-width: none;
    }
}
