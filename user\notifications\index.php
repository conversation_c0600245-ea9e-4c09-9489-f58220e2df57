<?php
/**
 * Bamboo User Application - Notifications Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: User notifications page showing all alerts and messages
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Handle notification actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'mark_read') {
            $notification_id = intval($_POST['notification_id'] ?? 0);
            if ($notification_id > 0) {
                // Mark notification as read
                updateRecord('user_notifications', 
                    ['is_read' => 1, 'read_at' => date('Y-m-d H:i:s')], 
                    ['id' => $notification_id, 'user_id' => $user_id]
                );
                showSuccess('Notification marked as read.');
            }
        } elseif ($action === 'mark_all_read') {
            // Mark all notifications as read
            updateRecord('user_notifications', 
                ['is_read' => 1, 'read_at' => date('Y-m-d H:i:s')], 
                ['user_id' => $user_id, 'is_read' => 0]
            );
            showSuccess('All notifications marked as read.');
        } elseif ($action === 'delete') {
            $notification_id = intval($_POST['notification_id'] ?? 0);
            if ($notification_id > 0) {
                // Delete notification
                deleteRecord('user_notifications', ['id' => $notification_id, 'user_id' => $user_id]);
                showSuccess('Notification deleted.');
            }
        }
    }
}

// Get pagination parameters
$page = intval($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// Get total notifications count
$total_notifications = fetchRow("SELECT COUNT(*) as count FROM user_notifications WHERE user_id = ?", [$user_id])['count'] ?? 0;
$total_pages = ceil($total_notifications / $limit);

// Get notifications with pagination
$notifications = fetchAll("
    SELECT * FROM user_notifications
    WHERE user_id = ?
    ORDER BY created_at DESC
    LIMIT ? OFFSET ?
", [$user_id, $limit, $offset]);

// Auto-mark all notifications as read when user opens the page
if (!empty($notifications)) {
    $notification_ids = array_column($notifications, 'id');
    if (!empty($notification_ids)) {
        $placeholders = str_repeat('?,', count($notification_ids) - 1) . '?';
        $params = array_merge($notification_ids, [$user_id]);
        executeQuery("
            UPDATE user_notifications
            SET is_read = 1, read_at = NOW()
            WHERE id IN ($placeholders) AND user_id = ? AND is_read = 0
        ", $params);

        // Update the notifications array to reflect the read status
        foreach ($notifications as &$notification) {
            $notification['is_read'] = 1;
        }
    }
}

// Get unread count (should be 0 after auto-marking)
$unread_count = getUserUnreadNotificationsCount($user_id);

// Page configuration
$page_title = 'Notifications';
$page_description = 'View all your notifications and alerts';
$page_css = 'notifications.css';
$page_js = 'notifications.js';

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Hero Section -->
    <div class="notifications-hero user-fade-in">
        <div class="hero-content">
            <h1 class="hero-title">Notifications</h1>
            <p class="hero-description">Stay updated with all your account activities and important alerts</p>
        </div>
    </div>



    <!-- Notifications List -->
    <div class="notifications-list user-fade-in">
        <?php if (!empty($notifications)): ?>
            <?php foreach ($notifications as $notification): ?>
                <div class="notification-item <?php echo $notification['is_read'] ? 'read' : 'unread'; ?>" data-id="<?php echo $notification['id']; ?>">
                    <div class="notification-content">
                        <div class="notification-header">
                            <div class="notification-icon">
                                <?php
                                $icon = '🔔';
                                switch ($notification['type']) {
                                    case 'salary': $icon = '💰'; break;
                                    case 'deposit': $icon = '💳'; break;
                                    case 'withdrawal': $icon = '💸'; break;
                                    case 'task': $icon = '✅'; break;
                                    case 'system': $icon = '⚙️'; break;
                                    case 'welcome': $icon = '🎉'; break;
                                    default: $icon = '🔔'; break;
                                }
                                echo $icon;
                                ?>
                            </div>
                            <div class="notification-meta">
                                <span class="notification-type"><?php echo ucfirst($notification['type']); ?></span>
                                <span class="notification-time"><?php echo date('Y-m-d H:i:s', strtotime($notification['created_at'])); ?></span>
                            </div>
                            <div class="notification-actions">
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    <input type="hidden" name="action" value="delete">
                                    <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                    <button type="submit" class="action-btn delete-btn" title="Delete notification" onclick="return confirm('Delete this notification?')">
                                        🗑️
                                    </button>
                                </form>
                            </div>
                        </div>
                        <div class="notification-message">
                            <?php echo htmlspecialchars($notification['message']); ?>
                        </div>
                        <?php if (!empty($notification['title']) && $notification['title'] !== $notification['message']): ?>
                        <div class="notification-title">
                            <?php echo htmlspecialchars($notification['title']); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php if (!$notification['is_read']): ?>
                    <div class="unread-indicator"></div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="no-notifications">
                <div class="no-notifications-icon">🔔</div>
                <h5>No Notifications</h5>
                <p>You don't have any notifications yet. When you receive alerts about your account activities, they will appear here.</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
    <div class="notifications-pagination user-fade-in">
        <div class="user-card">
            <div class="user-card-body">
                <nav aria-label="Notifications pagination">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>">Previous</a>
                        </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                        </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>">Next</a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php include '../includes/user_footer.php'; ?>
