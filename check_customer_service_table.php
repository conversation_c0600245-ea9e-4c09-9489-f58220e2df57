<?php
define('BAMBOO_APP', true);
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check table structure
echo "Checking customer_service_contacts table structure:\n";
$columns = fetchAll("DESCRIBE customer_service_contacts");
foreach ($columns as $column) {
    echo "Column: " . $column['Field'] . " - Type: " . $column['Type'] . "\n";
}

echo "\nChecking existing data:\n";
$contacts = fetchAll("SELECT * FROM customer_service_contacts");
foreach ($contacts as $contact) {
    echo "ID: " . $contact['id'] . " - Name: " . $contact['name'] . " - Link: " . $contact['link'] . "\n";
}

// Add missing columns if needed
echo "\nAdding missing columns if needed:\n";

// Check if is_active column exists
$has_is_active = false;
$has_sort_order = false;
foreach ($columns as $column) {
    if ($column['Field'] === 'is_active') {
        $has_is_active = true;
    }
    if ($column['Field'] === 'sort_order') {
        $has_sort_order = true;
    }
}

if (!$has_is_active) {
    $result = executeQuery("ALTER TABLE customer_service_contacts ADD COLUMN is_active TINYINT(1) DEFAULT 1");
    echo "Added is_active column\n";
}

if (!$has_sort_order) {
    $result = executeQuery("ALTER TABLE customer_service_contacts ADD COLUMN sort_order INT DEFAULT 0");
    echo "Added sort_order column\n";
}

// Update existing records to be active
executeQuery("UPDATE customer_service_contacts SET is_active = 1 WHERE is_active IS NULL");
echo "Updated existing records to be active\n";

echo "\nFinal data check:\n";
$contacts = fetchAll("SELECT * FROM customer_service_contacts WHERE is_active = 1 ORDER BY sort_order ASC");
foreach ($contacts as $contact) {
    echo "ID: " . $contact['id'] . " - Name: " . $contact['name'] . " - Active: " . ($contact['is_active'] ?? 'NULL') . "\n";
}
?>
