<?php
/**
 * Interactive SQL Query Tool for Bamboo Database
 * Allows running custom SQL queries safely
 */

require_once 'includes/config.php';
require_once 'includes/database.php';

echo "=== BAMBOO INTERACTIVE SQL TOOL ===\n\n";

// Check if query is provided as command line argument
$query = '';
if ($argc > 1) {
    $query = $argv[1];
} else {
    echo "Usage: php interactive_sql_tool.php \"YOUR SQL QUERY\"\n";
    echo "Example: php interactive_sql_tool.php \"SELECT COUNT(*) as total_users FROM users\"\n\n";
    
    echo "Quick queries you can try:\n";
    echo "1. \"SELECT COUNT(*) as total_users FROM users\"\n";
    echo "2. \"SELECT username, balance, vip_level FROM users ORDER BY balance DESC\"\n";
    echo "3. \"SELECT status, COUNT(*) as count FROM tasks GROUP BY status\"\n";
    echo "4. \"SELECT type, SUM(amount) as total FROM transactions GROUP BY type\"\n";
    echo "5. \"SHOW TABLES\"\n";
    echo "6. \"DESCRIBE users\"\n\n";
    exit;
}

try {
    $db = getDB();
    
    // Security check - only allow SELECT, SHOW, DESCRIBE queries
    $query_upper = strtoupper(trim($query));
    $allowed_commands = ['SELECT', 'SHOW', 'DESCRIBE', 'EXPLAIN'];
    $is_allowed = false;
    
    foreach ($allowed_commands as $command) {
        if (strpos($query_upper, $command) === 0) {
            $is_allowed = true;
            break;
        }
    }
    
    if (!$is_allowed) {
        echo "❌ Error: Only SELECT, SHOW, DESCRIBE, and EXPLAIN queries are allowed for security.\n";
        exit;
    }
    
    echo "Executing query: $query\n";
    echo str_repeat("=", 80) . "\n";
    
    $stmt = $db->query($query);
    $results = $stmt->fetchAll();
    
    if (empty($results)) {
        echo "No results found.\n";
    } else {
        // Get column names
        $columns = array_keys($results[0]);
        
        // Calculate column widths
        $widths = [];
        foreach ($columns as $col) {
            $widths[$col] = max(strlen($col), 10); // Minimum width of 10
        }
        
        // Check data to adjust widths
        foreach ($results as $row) {
            foreach ($row as $col => $value) {
                $widths[$col] = max($widths[$col], strlen($value));
            }
        }
        
        // Limit maximum width to 30 characters
        foreach ($widths as $col => $width) {
            $widths[$col] = min($width, 30);
        }
        
        // Print header
        foreach ($columns as $col) {
            echo sprintf("%-{$widths[$col]}s | ", substr($col, 0, $widths[$col]));
        }
        echo "\n";
        
        // Print separator
        foreach ($columns as $col) {
            echo str_repeat("-", $widths[$col]) . "-+-";
        }
        echo "\n";
        
        // Print data rows
        foreach ($results as $row) {
            foreach ($columns as $col) {
                $value = $row[$col];
                if ($value === null) {
                    $value = 'NULL';
                } elseif (strlen($value) > $widths[$col]) {
                    $value = substr($value, 0, $widths[$col] - 3) . '...';
                }
                echo sprintf("%-{$widths[$col]}s | ", $value);
            }
            echo "\n";
        }
        
        echo "\nTotal rows: " . count($results) . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error executing query: " . $e->getMessage() . "\n";
}

echo "\n=== QUERY COMPLETE ===\n";
?>