<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

try {
    // Check recent transactions
    $stmt = $pdo->prepare('SELECT id, amount, status, contact_service_id, created_at FROM transactions WHERE type = "deposit" ORDER BY created_at DESC LIMIT 5');
    $stmt->execute();
    $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Recent Deposit Transactions:\n";
    foreach ($transactions as $txn) {
        echo "ID: {$txn['id']}, Amount: {$txn['amount']}, Status: {$txn['status']}, Contact: {$txn['contact_service_id']}, Date: {$txn['created_at']}\n";
    }
    
    // Update one transaction to completed for testing (if we have at least 2 transactions)
    if (count($transactions) >= 2) {
        $stmt = $pdo->prepare('UPDATE transactions SET status = "completed" WHERE id = ? AND status = "pending"');
        $stmt->execute([$transactions[1]['id']]);
        
        echo "\nUpdated transaction {$transactions[1]['id']} to completed status for testing.\n";
        echo "Now you can test the deposit page to see the difference between pending and completed transactions.\n";
    }
    
} catch (PDOException $e) {
    echo 'Database error: ' . $e->getMessage();
}
?>