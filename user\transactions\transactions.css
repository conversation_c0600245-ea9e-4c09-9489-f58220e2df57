/**
 * Bamboo User Dashboard - Transactions Page CSS
 * Company: Notepadsly
 * Version: 1.0
 * Description: Styling for the transactions page with proper layout structure
 */

/* ===== PAGE LAYOUT FIXES ===== */
/* Fix footer positioning by ensuring proper page structure */
body.user-dashboard {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.user-main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.user-container {
    flex: 1;
    padding-bottom: 2rem; /* Add bottom padding to prevent footer overlap */
}

/* Ensure footer stays at bottom */
.user-footer {
    margin-top: auto;
    flex-shrink: 0;
}

/* ===== TRANSACTIONS PAGE STYLES ===== */
.transactions-header {
    margin-bottom: 2rem;
}

.header-content {
    text-align: center;
    margin-bottom: 2rem;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--user-text-primary);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--user-primary), var(--user-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    font-size: 1.1rem;
    color: var(--user-text-secondary);
    margin: 0;
}

/* ===== SUMMARY CARDS ===== */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: var(--user-card-bg);
    border: var(--user-border-width) solid var(--user-border-color);
    border-radius: var(--user-border-radius-lg);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: var(--user-shadow);
    transition: var(--user-transition);
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--user-shadow-lg);
}

.summary-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: white;
    flex-shrink: 0;
}

.deposits-card .card-icon {
    background: linear-gradient(135deg, var(--user-success), #20c997);
}

.withdrawals-card .card-icon {
    background: linear-gradient(135deg, var(--user-warning), #fd7e14);
}

.commissions-card .card-icon {
    background: linear-gradient(135deg, var(--user-info), #0dcaf0);
}

.balance-card .card-icon {
    background: linear-gradient(135deg, var(--user-primary), var(--user-accent));
}

.summary-card .card-content h3 {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--user-text-secondary);
    margin: 0 0 0.25rem 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.summary-card .amount {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--user-text-primary);
    margin: 0;
    white-space: nowrap;
}

/* ===== TRANSACTION FILTERS ===== */
.transaction-filters {
    background: var(--user-card-bg);
    border: var(--user-border-width) solid var(--user-border-color);
    border-radius: var(--user-border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--user-shadow);
}

.filter-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--user-text-primary);
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-title::before {
    content: "🔍";
    font-size: 1rem;
}

.filter-form {
    width: 100%;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--user-text-secondary);
}

.filter-select,
.filter-input {
    padding: 0.75rem;
    border: var(--user-border-width) solid var(--user-border-color);
    border-radius: var(--user-border-radius);
    font-size: 0.9rem;
    background: var(--user-background);
    color: var(--user-text-primary);
    transition: var(--user-transition);
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: var(--user-primary);
    box-shadow: 0 0 0 3px rgba(255, 105, 0, 0.1);
}

.filter-actions {
    display: flex;
    gap: 0.5rem;
    align-items: end;
}

/* ===== TRANSACTION STATEMENT ===== */
.transaction-statement {
    background: var(--user-card-bg);
    border: var(--user-border-width) solid var(--user-border-color);
    border-radius: var(--user-border-radius-lg);
    box-shadow: var(--user-shadow);
    margin-bottom: 2rem;
}

.statement-header {
    padding: 1.5rem;
    border-bottom: var(--user-border-width) solid var(--user-border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.statement-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--user-text-primary);
    margin: 0;
}

.statement-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.total-count {
    font-size: 0.9rem;
    color: var(--user-text-secondary);
    background: var(--user-border-light);
    padding: 0.25rem 0.75rem;
    border-radius: var(--user-border-radius);
}

/* ===== TRANSACTION TABLE ===== */
.transaction-table-container {
    overflow-x: auto;
}

.transaction-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.transaction-table th {
    background: var(--user-border-light);
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    color: var(--user-text-secondary);
    border-bottom: var(--user-border-width) solid var(--user-border-color);
    white-space: nowrap;
}

.transaction-table td {
    padding: 1rem 0.75rem;
    border-bottom: var(--user-border-width) solid var(--user-border-light);
    vertical-align: middle;
}

.transaction-row:hover {
    background: rgba(255, 105, 0, 0.02);
}

.date-cell .date-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.date-cell .date {
    font-weight: 500;
    color: var(--user-text-primary);
}

.date-cell .time {
    font-size: 0.8rem;
    color: var(--user-text-muted);
}

.transaction-id {
    font-family: monospace;
    font-size: 0.8rem;
    color: var(--user-text-secondary);
    background: var(--user-border-light);
    padding: 0.25rem 0.5rem;
    border-radius: var(--user-border-radius);
}

.type-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.type-info i {
    width: 20px;
    text-align: center;
}

.amount {
    font-weight: 600;
    font-size: 1rem;
}

.amount.positive {
    color: var(--user-success);
}

.amount.negative {
    color: var(--user-danger);
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--user-border-radius);
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: rgba(255, 193, 7, 0.1);
    color: var(--user-warning);
}

.status-processing {
    background: rgba(23, 162, 184, 0.1);
    color: var(--user-info);
}

.status-completed {
    background: rgba(40, 167, 69, 0.1);
    color: var(--user-success);
}

.status-failed,
.status-cancelled {
    background: rgba(220, 53, 69, 0.1);
    color: var(--user-danger);
}

.balance-amount {
    font-family: monospace;
    font-weight: 500;
    color: var(--user-text-primary);
}

.details-btn {
    background: var(--user-primary);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--user-border-radius);
    font-size: 0.8rem;
    cursor: pointer;
    transition: var(--user-transition);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.details-btn:hover {
    background: var(--user-accent);
    transform: translateY(-1px);
}

/* ===== PAGINATION ===== */
.pagination-container {
    padding: 1.5rem;
    border-top: var(--user-border-width) solid var(--user-border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.pagination-numbers {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    flex-wrap: wrap;
}

.pagination-btn,
.pagination-number {
    padding: 0.5rem 1rem;
    border: var(--user-border-width) solid var(--user-border-color);
    border-radius: var(--user-border-radius);
    text-decoration: none;
    color: var(--user-text-primary);
    background: var(--user-background);
    transition: var(--user-transition);
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
}

.pagination-btn:hover,
.pagination-number:hover {
    background: var(--user-primary);
    color: white;
    border-color: var(--user-primary);
}

.pagination-number.active {
    background: var(--user-primary);
    color: white;
    border-color: var(--user-primary);
}

.pagination-dots {
    padding: 0.5rem;
    color: var(--user-text-muted);
}

.pagination-info {
    font-size: 0.9rem;
    color: var(--user-text-secondary);
}

/* ===== NO TRANSACTIONS STATE ===== */
.no-transactions {
    padding: 3rem 1.5rem;
    text-align: center;
}

.empty-state {
    max-width: 400px;
    margin: 0 auto;
}

.empty-state i {
    font-size: 4rem;
    color: var(--user-text-muted);
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    color: var(--user-text-primary);
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: var(--user-text-secondary);
    margin-bottom: 1.5rem;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== QUICK ACTIONS ===== */
.quick-actions {
    background: var(--user-card-bg);
    border: var(--user-border-width) solid var(--user-border-color);
    border-radius: var(--user-border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--user-shadow);
    margin-bottom: 2rem;
}

.actions-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--user-text-primary);
    margin: 0 0 1rem 0;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--user-background);
    border: var(--user-border-width) solid var(--user-border-color);
    border-radius: var(--user-border-radius);
    text-decoration: none;
    color: var(--user-text-primary);
    transition: var(--user-transition);
    font-weight: 500;
}

.action-btn:hover {
    background: var(--user-primary);
    color: white;
    border-color: var(--user-primary);
    transform: translateY(-2px);
    box-shadow: var(--user-shadow);
}

.action-btn i {
    font-size: 1.2rem;
}

/* ===== MODAL STYLES ===== */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: var(--user-card-bg);
    margin: 5% auto;
    padding: 0;
    border-radius: var(--user-border-radius-lg);
    width: 90%;
    max-width: 600px;
    box-shadow: var(--user-shadow-lg);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: var(--user-border-width) solid var(--user-border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--user-text-primary);
}

.modal-close {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--user-text-muted);
    transition: var(--user-transition);
}

.modal-close:hover {
    color: var(--user-text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: var(--user-border-width) solid var(--user-border-color);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 992px) {
    .pagination-container {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }

    .pagination-numbers {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .filter-row {
        grid-template-columns: 1fr;
    }
    
    .filter-actions {
        justify-content: stretch;
    }
    
    .filter-actions .user-btn {
        flex: 1;
    }
    
    .statement-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .transaction-table {
        font-size: 0.8rem;
    }
    
    .transaction-table th,
    .transaction-table td {
        padding: 0.75rem 0.5rem;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .empty-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .user-container {
        padding: 0 1rem;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .summary-card {
        padding: 1rem;
    }
    
    .summary-card .card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .summary-card .amount {
        font-size: 1.2rem;
    }
    
    .transaction-filters,
    .transaction-statement,
    .quick-actions {
        margin-left: -1rem;
        margin-right: -1rem;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
}

/* ===== ANIMATION CLASSES ===== */
.user-fade-in {
    animation: fadeIn 0.6s ease forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== ICON STYLES ===== */
.icon-deposit::before { content: "💰"; }
.icon-withdraw::before { content: "💸"; }
.icon-commission::before { content: "💼"; }
.icon-balance::before { content: "💳"; }
.icon-filter::before { content: "🔍"; }
.icon-refresh::before { content: "🔄"; }
.icon-transactions::before { content: "📊"; }
.icon-info::before { content: "ℹ️"; }
.icon-chevron-left::before { content: "◀"; }
.icon-chevron-right::before { content: "▶"; }
.icon-user::before { content: "👤"; }
.icon-dashboard::before { content: "📈"; }
.icon-bonus::before { content: "🎁"; }
.icon-penalty::before { content: "⚠️"; }
.icon-adjustment::before { content: "⚖️"; }
.icon-admin_credit::before { content: "➕"; }
.icon-admin_deduction::before { content: "➖"; }
.icon-referral_bonus::before { content: "🤝"; }