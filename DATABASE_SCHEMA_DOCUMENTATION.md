# Bamboo Database Schema Documentation

## Overview
The Bamboo application is a sophisticated task-based earning platform with multi-level marketing features. The database consists of 18 tables managing users, tasks, products, transactions, and administrative functions.

## Database Configuration
- **Database Name**: `matchmaking`
- **Host**: `localhost:3306`
- **Engine**: MySQL
- **Character Set**: utf8mb4

## Core Statistics (Current)
- **Total Users**: 4 (all active)
- **Total Balance**: $246,493.37
- **Total Transactions**: 66
- **Total Tasks**: 46 (97.83% completed)
- **VIP Levels**: 5 levels defined

## Table Structure

### 1. User Management Tables

#### `users` (4 records)
**Primary user accounts table**
- `id` (PK) - Auto-increment user ID
- `username` (UNIQUE) - User login name
- `phone` (UNIQUE) - Phone number
- `email` (UNIQUE) - Email address
- `password_hash` - Encrypted password
- `withdrawal_pin_hash` - Withdrawal PIN
- `balance` - Current account balance
- `commission_balance` - Earned commission balance
- `frozen_balance` - Temporarily frozen funds
- `vip_level` (FK) - References vip_levels.level
- `invitation_code` (UNIQUE) - User's referral code
- `invited_by` - Superior's invitation code
- `status` - ENUM: pending, active, suspended, banned
- Financial tracking fields for deposits, withdrawals, commissions
- Activity tracking: tasks_completed_today, last_task_date, last_login

#### `user_sessions` (0 records)
**Session management**
- `id` (PK) - Session ID
- `user_id` (FK) - References users.id
- `ip_address` - User's IP
- `user_agent` - Browser information
- `payload` - Session data
- `last_activity` - Timestamp

#### `user_dashboard_view` (4 records)
**Optimized view for dashboard display**
- Aggregated user data for quick dashboard loading
- Includes balance, VIP info, task counts, commission totals

#### `user_salaries` (2 records)
**Salary payments to users**
- `user_id` (FK) - References users.id
- `amount` - Salary amount
- `status` - ENUM: paid, pending_approval
- `admin_id_processed` - Admin who processed payment

### 2. Administrative Tables

#### `admin_users` (2 records)
**Administrative accounts**
- `id` (PK) - Admin ID
- `username` (UNIQUE) - Admin login
- `email` (UNIQUE) - Admin email
- `full_name` - Admin's full name
- `password_hash` - Encrypted password
- `role` - ENUM: super_admin, admin, moderator
- `permissions` - JSON permissions object
- `status` - ENUM: active, inactive

#### `admin_user_stats` (1 record)
**Aggregated statistics for admin dashboard**
- Real-time statistics: total_users, active_users, new_today
- Financial totals: total_balance, total_deposits, total_withdrawals
- Auto-updated via triggers

### 3. Task & Product System

#### `tasks` (46 records)
**User task assignments and completions**
- `id` (PK) - Task ID
- `user_id` (FK) - References users.id
- `product_id` (FK) - References products.id
- `amount` - Task amount
- `commission_earned` - Commission from task
- `base_commission` - Base commission rate
- `vip_bonus` - VIP level bonus
- `status` - ENUM: assigned, in_progress, completed, failed, expired
- `assigned_at`, `started_at`, `completed_at` - Timestamps
- `submission_data` - JSON task submission data
- `is_negative_trigger` - Special negative balance trigger
- `appraisal_no` (UNIQUE) - Task appraisal number

#### `products` (25 records)
**Available products for tasks**
- `id` (PK) - Product ID
- `name` - Product name
- `description` - Product description
- `image_url` - Product image
- `price` - Product price
- `commission_rate` - Commission percentage
- `category_id` (FK) - References product_categories.id
- `min_vip_level` (FK) - Minimum VIP level required
- `max_daily_assignments` - Daily assignment limit
- `weight` - Assignment probability weight
- `stock` - Available stock
- `status` - ENUM: active, inactive, out_of_stock
- `total_assignments`, `total_completions` - Statistics

#### `product_categories` (5 records)
**Product categorization**
- Electronics, Fashion, Home & Garden, Sports, Beauty

#### `negative_settings` (2 records)
**Special task configurations for negative balance triggers**
- `user_id` (FK) - Target user
- `trigger_task_number` - Task number to trigger
- `product_id_override` (FK) - Override product
- `override_amount` - Override amount
- `is_active`, `is_triggered` - Status flags

### 4. Financial System

#### `transactions` (66 records)
**All financial transactions**
- `id` (PK) - Transaction ID
- `order_no` (UNIQUE) - Order number
- `user_id` (FK) - References users.id
- `type` - ENUM: deposit, withdrawal, commission, bonus, referral_bonus, penalty, adjustment, admin_credit, admin_deduction
- `amount` - Transaction amount
- `balance_before`, `balance_after` - Balance snapshots
- `status` - ENUM: pending, processing, completed, failed, cancelled
- `payment_method` - Payment method used
- `transaction_id` (UNIQUE) - External transaction ID
- `fee_amount` - Transaction fees
- `processed_by` (FK) - Admin who processed
- Financial tracking: payment_channel, credited_by, state

#### `vip_levels` (5 records)
**VIP tier definitions**
- `level` (UNIQUE) - VIP level number (1-5)
- `name` - VIP level name
- `min_balance` - Minimum balance required
- `max_daily_tasks` - Maximum daily tasks allowed
- `commission_multiplier` - Commission bonus multiplier
- `withdrawal_limit_daily` - Daily withdrawal limit
- `withdrawal_fee_percentage` - Withdrawal fee
- `benefits` - Text description of benefits
- `icon_path` - VIP level icon

#### `withdrawal_quotes` (0 records)
**Withdrawal request management**
- `user_id` (FK) - User requesting withdrawal
- `message` - Withdrawal request message
- `status` - ENUM: active, resolved

### 5. Communication & Settings

#### `notifications` (1 record)
**System notifications and banners**
- `type` - ENUM: system, user, admin, banner
- `title`, `message` - Notification content
- `target_user_id` (FK) - Specific user target
- `target_vip_level` - VIP level target
- `is_global`, `is_popup`, `is_banner` - Display flags
- `banner_color` - Banner color
- `status` - ENUM: active, inactive, scheduled
- `start_date`, `end_date` - Display period
- `created_by` (FK) - Admin creator

#### `settings` (51 records)
**Application configuration**
- `key` (UNIQUE) - Setting key
- `value` - Setting value
- `type` - ENUM: string, integer, float, boolean, json
- `description` - Setting description
- `category` - Setting category
- `is_public` - Public visibility flag
- `updated_by` (FK) - Admin who updated

#### `customer_service_contacts` (2 records)
**Support contact information**
- Customer service links and contact details

#### `demo_features` (3 records)
**Feature demonstrations**
- Task Automation, Real-time Notifications, Advanced Analytics

### 6. Referral System

#### `superiors` (1 record)
**Referral/invitation system management**
- `name` - Superior name
- `phone`, `email` - Contact information
- `invitation_code` (UNIQUE) - Referral code
- `invited_count` - Number of successful invitations

## Key Relationships

### Foreign Key Constraints
1. `notifications.target_user_id` → `users.id`
2. `notifications.created_by` → `admin_users.id`
3. `products.category_id` → `product_categories.id`
4. `products.min_vip_level` → `vip_levels.level`
5. `settings.updated_by` → `admin_users.id`
6. `tasks.user_id` → `users.id`
7. `tasks.product_id` → `products.id`
8. `transactions.user_id` → `users.id`
9. `transactions.processed_by` → `admin_users.id`
10. `user_sessions.user_id` → `users.id`
11. `users.vip_level` → `vip_levels.level`

### Key Indexes
- All primary keys have unique indexes
- Username, email, phone have unique indexes for users
- Composite indexes for performance:
  - `idx_task_user_status` on tasks
  - `idx_transaction_user_type` on transactions
  - `idx_user_vip_status` on users

## Business Logic Insights

### User Progression
- Users start at VIP Level 1 with basic benefits
- VIP progression based on balance thresholds
- Higher VIP levels get more daily tasks and better commission rates

### Task System
- Products assigned to users as tasks
- Commission earned based on product commission rate × VIP multiplier
- Negative settings can override normal task behavior

### Financial Flow
- Users deposit money to participate
- Complete tasks to earn commissions
- Withdraw earnings (subject to VIP limits and fees)
- Admin can credit/deduct balances manually

### Referral System
- Users invited via superior invitation codes
- Referral tracking through `invited_by` field
- Superior statistics tracked in `superiors` table

## Current Platform Statistics

### User Distribution
- **VIP 1**: 2 users (50%) - Avg Balance: $28,671.69
- **VIP 2**: 1 user (25%) - Avg Balance: $56,150.00
- **VIP 3**: 1 user (25%) - Avg Balance: $133,000.00
- **VIP 4-5**: 0 users

### Task Performance
- **97.83%** completion rate (45/46 tasks completed)
- Most popular product: iPhone 15 Pro (17 tasks)
- Total commissions paid: $2,981.76

### Financial Overview
- **Total Deposits**: $79,072,027.00
- **Total Withdrawals**: $79,245,311.00
- **Net Platform Balance**: -$173,284.00 (more withdrawn than deposited)
- **User Balances**: $246,493.37
- **Commission Balances**: $586.43

### Activity Patterns
- Most active user: demohomexx (38 tasks in 30 days)
- Average tasks per active user: 11.5
- Last activity: July 21, 2025

## Recommendations for Analysis

### Performance Queries
1. **User Activity Monitoring**: Track daily/weekly task completion rates
2. **Financial Health**: Monitor deposit vs withdrawal ratios
3. **VIP Progression**: Analyze user advancement through VIP levels
4. **Product Performance**: Identify most/least popular products
5. **Referral Effectiveness**: Track invitation success rates

### Data Integrity Checks
1. **Balance Reconciliation**: Ensure user balances match transaction history
2. **Task Completion Validation**: Verify commission calculations
3. **VIP Level Consistency**: Check users meet VIP level requirements
4. **Referral Chain Validation**: Verify invitation code relationships

### Security Considerations
1. **Transaction Monitoring**: Watch for unusual transaction patterns
2. **User Behavior Analysis**: Detect potential fraud or abuse
3. **Admin Action Tracking**: Monitor administrative changes
4. **Session Management**: Track user login patterns

This documentation provides a comprehensive overview of the Bamboo database structure and current state, enabling effective analysis and maintenance of the platform.