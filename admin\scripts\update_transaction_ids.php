<?php
/**
 * Update existing transactions with transaction IDs
 * Run this script once to add transaction IDs to existing records
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Check if running from command line or admin access
if (php_sapi_name() !== 'cli') {
    session_start();
    if (!isAdminLoggedIn()) {
        die('Access denied. Admin login required.');
    }
}

echo "Starting transaction ID update process...\n";

try {
    // Get all transactions without transaction_id
    $transactions = fetchAll("SELECT id, type, created_at FROM transactions WHERE transaction_id IS NULL OR transaction_id = ''");
    
    if (empty($transactions)) {
        echo "No transactions found that need updating.\n";
        exit;
    }
    
    echo "Found " . count($transactions) . " transactions to update.\n";
    
    $updated_count = 0;
    
    foreach ($transactions as $transaction) {
        // Generate transaction ID based on type
        $type_prefix = '';
        switch ($transaction['type']) {
            case 'deposit':
                $type_prefix = 'DEP';
                break;
            case 'withdrawal':
                $type_prefix = 'WTH';
                break;
            case 'commission':
                $type_prefix = 'COM';
                break;
            case 'bonus':
                $type_prefix = 'BON';
                break;
            case 'referral_bonus':
                $type_prefix = 'REF';
                break;
            default:
                $type_prefix = 'TXN';
                break;
        }
        
        // Generate unique transaction ID
        $transaction_id = generateTransactionId($type_prefix);
        
        // Update the transaction
        $result = updateRecord('transactions', 
            ['transaction_id' => $transaction_id], 
            'id = ?', 
            [$transaction['id']]
        );
        
        if ($result) {
            $updated_count++;
            echo "Updated transaction ID {$transaction['id']} with {$transaction_id}\n";
        } else {
            echo "Failed to update transaction ID {$transaction['id']}\n";
        }
    }
    
    echo "\nUpdate complete! Updated {$updated_count} out of " . count($transactions) . " transactions.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
