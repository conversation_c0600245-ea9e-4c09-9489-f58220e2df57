/**
 * Bamboo User Application - Personal Information Page Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* Personal Info Card */
.personal-info-card {
    width: 100%;
    margin: 0 auto;
}

/* Hero Section */
.personal-info-hero {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    padding: 3rem 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.hero-content {
    max-width: 600px;
    margin: 0 auto;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-description {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .personal-info-hero {
        padding: 2rem 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }
}

.info-status {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Info Sections */
.info-section {
    margin-bottom: var(--user-spacing-xl);
    padding-bottom: var(--user-spacing-lg);
    border-bottom: 1px solid var(--user-border-color);
}

.info-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--user-text-primary);
    margin-bottom: var(--user-spacing-md);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    border-radius: 2px;
}



/* Info Grid */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--user-spacing-lg);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--user-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1rem;
    font-weight: 500;
    color: var(--user-text-primary);
    padding: 0.75rem;
    background: var(--user-bg-secondary);
    border-radius: var(--user-border-radius);
    border: 1px solid var(--user-border-color);
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

/* Security Indicators */
.security-indicator {
    font-weight: 500;
}

.referral-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--user-primary);
}

.credit-score {
    font-weight: 600;
    color: var(--user-primary);
    font-size: 1.125rem;
}

.user-id-highlight {
    font-family: 'Courier New', monospace;
    font-weight: 700;
    color: var(--user-primary);
    font-size: 1.125rem;
    background: rgba(var(--user-primary-rgb), 0.1);
    border-left: 4px solid var(--user-primary);
}

/* Admin Contact Notice */
.admin-contact-notice {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 1px solid #f59e0b;
    border-radius: var(--user-border-radius-lg);
    padding: var(--user-spacing-lg);
    margin: var(--user-spacing-xl) 0;
    display: flex;
    gap: var(--user-spacing-md);
    align-items: flex-start;
}

.notice-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.notice-content h6 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #92400e;
    margin-bottom: 0.5rem;
}

.notice-content p {
    color: #92400e;
    margin-bottom: var(--user-spacing-md);
    line-height: 1.5;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--user-spacing-md);
    justify-content: center;
    flex-wrap: wrap;
    margin-top: var(--user-spacing-xl);
}

/* Responsive Design */
@media (max-width: 768px) {
    .personal-info-page-title .page-title {
        font-size: 1.5rem;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
        gap: var(--user-spacing-md);
    }
    
    .admin-contact-notice {
        flex-direction: column;
        text-align: center;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .action-buttons .user-btn {
        width: 100%;
        max-width: 250px;
    }
}

/* Animation */
.user-fade-in {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
