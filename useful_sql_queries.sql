-- =====================================================
-- BAMBOO DATABASE - USEFUL SQL QUERIES COLLECTION
-- =====================================================

-- 1. USER BALANCE RECONCILIATION
-- Verify user balances match their transaction history
SELECT 
    u.id,
    u.username,
    u.balance as current_balance,
    COALESCE(SUM(
        CASE 
            WHEN t.type IN ('deposit', 'commission', 'bonus', 'referral_bonus', 'admin_credit') 
            THEN t.amount
            WHEN t.type IN ('withdrawal', 'penalty', 'admin_deduction') 
            THEN -t.amount
            ELSE 0
        END
    ), 0) as calculated_balance,
    (u.balance - COALESCE(SUM(
        CASE 
            WHEN t.type IN ('deposit', 'commission', 'bonus', 'referral_bonus', 'admin_credit') 
            THEN t.amount
            WHEN t.type IN ('withdrawal', 'penalty', 'admin_deduction') 
            THEN -t.amount
            ELSE 0
        END
    ), 0)) as balance_difference
FROM users u
LEFT JOIN transactions t ON u.id = t.user_id AND t.status = 'completed'
GROUP BY u.id, u.username, u.balance
HAVING ABS(balance_difference) > 0.01
ORDER BY ABS(balance_difference) DESC;

-- 2. DAILY TASK COMPLETION TRENDS
-- Track task completion patterns over time
SELECT 
    DATE(completed_at) as completion_date,
    COUNT(*) as tasks_completed,
    COUNT(DISTINCT user_id) as active_users,
    SUM(commission_earned) as total_commission,
    AVG(commission_earned) as avg_commission,
    COUNT(CASE WHEN is_negative_trigger = 1 THEN 1 END) as negative_triggers
FROM tasks 
WHERE status = 'completed' 
    AND completed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(completed_at)
ORDER BY completion_date DESC;

-- 3. VIP LEVEL PROGRESSION ANALYSIS
-- Users who should be eligible for VIP upgrades
SELECT 
    u.id,
    u.username,
    u.balance,
    u.vip_level as current_vip,
    v_current.name as current_vip_name,
    v_next.level as eligible_vip_level,
    v_next.name as eligible_vip_name,
    (v_next.max_daily_tasks - v_current.max_daily_tasks) as additional_tasks,
    (v_next.commission_multiplier - v_current.commission_multiplier) as commission_boost
FROM users u
JOIN vip_levels v_current ON u.vip_level = v_current.level
LEFT JOIN vip_levels v_next ON v_next.level = (
    SELECT MIN(level) 
    FROM vip_levels 
    WHERE level > u.vip_level AND min_balance <= u.balance
)
WHERE u.status = 'active' 
    AND v_next.level IS NOT NULL
ORDER BY u.balance DESC;

-- 4. PRODUCT PERFORMANCE ANALYSIS
-- Detailed product performance metrics
SELECT 
    p.id,
    p.name,
    p.price,
    p.commission_rate,
    pc.name as category,
    COUNT(t.id) as total_assignments,
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_tasks,
    COUNT(CASE WHEN t.status = 'failed' THEN 1 END) as failed_tasks,
    ROUND(
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(t.id), 0), 2
    ) as completion_rate,
    SUM(CASE WHEN t.status = 'completed' THEN t.commission_earned ELSE 0 END) as total_commission_paid,
    AVG(CASE WHEN t.status = 'completed' THEN t.commission_earned ELSE NULL END) as avg_commission,
    MAX(t.completed_at) as last_completed
FROM products p
LEFT JOIN product_categories pc ON p.category_id = pc.id
LEFT JOIN tasks t ON p.id = t.product_id
WHERE p.status = 'active'
GROUP BY p.id, p.name, p.price, p.commission_rate, pc.name
ORDER BY total_assignments DESC, completion_rate DESC;

-- 5. USER ACTIVITY HEATMAP
-- User activity patterns by hour and day
SELECT 
    DAYNAME(completed_at) as day_of_week,
    HOUR(completed_at) as hour_of_day,
    COUNT(*) as task_completions,
    COUNT(DISTINCT user_id) as unique_users,
    SUM(commission_earned) as total_commission
FROM tasks 
WHERE status = 'completed' 
    AND completed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DAYNAME(completed_at), HOUR(completed_at)
ORDER BY 
    FIELD(day_of_week, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'),
    hour_of_day;

-- 6. FINANCIAL HEALTH INDICATORS
-- Key financial metrics for platform health
SELECT 
    'Total Platform Liability' as metric,
    SUM(balance + commission_balance) as amount,
    'USD' as currency
FROM users
WHERE status = 'active'

UNION ALL

SELECT 
    'Total Deposits (Completed)' as metric,
    SUM(amount) as amount,
    'USD' as currency
FROM transactions 
WHERE type = 'deposit' AND status = 'completed'

UNION ALL

SELECT 
    'Total Withdrawals (Completed)' as metric,
    SUM(amount) as amount,
    'USD' as currency
FROM transactions 
WHERE type = 'withdrawal' AND status = 'completed'

UNION ALL

SELECT 
    'Pending Withdrawals' as metric,
    SUM(amount) as amount,
    'USD' as currency
FROM transactions 
WHERE type = 'withdrawal' AND status IN ('pending', 'processing')

UNION ALL

SELECT 
    'Total Commission Paid' as metric,
    SUM(amount) as amount,
    'USD' as currency
FROM transactions 
WHERE type = 'commission' AND status = 'completed';

-- 7. REFERRAL NETWORK ANALYSIS
-- Analyze the referral network structure
SELECT 
    s.name as superior_name,
    s.invitation_code,
    COUNT(u.id) as direct_referrals,
    AVG(u.balance) as avg_referral_balance,
    SUM(u.balance) as total_referral_balance,
    COUNT(CASE WHEN u.status = 'active' THEN 1 END) as active_referrals,
    COUNT(CASE WHEN u.last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recent_active,
    SUM(u.total_commission_earned) as total_referral_commissions
FROM superiors s
LEFT JOIN users u ON s.invitation_code = u.invited_by
GROUP BY s.id, s.name, s.invitation_code
ORDER BY direct_referrals DESC, total_referral_balance DESC;

-- 8. TASK ASSIGNMENT FAIRNESS
-- Check if task assignments are fair across users
SELECT 
    u.id,
    u.username,
    u.vip_level,
    v.max_daily_tasks,
    COUNT(t.id) as total_tasks_assigned,
    COUNT(CASE WHEN DATE(t.assigned_at) = CURDATE() THEN 1 END) as tasks_today,
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_tasks,
    ROUND(
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(t.id), 0), 2
    ) as completion_rate,
    u.last_task_date,
    DATEDIFF(CURDATE(), u.last_task_date) as days_since_last_task
FROM users u
JOIN vip_levels v ON u.vip_level = v.level
LEFT JOIN tasks t ON u.id = t.user_id
WHERE u.status = 'active'
GROUP BY u.id, u.username, u.vip_level, v.max_daily_tasks, u.last_task_date
ORDER BY u.vip_level DESC, completion_rate DESC;

-- 9. TRANSACTION ANOMALY DETECTION
-- Identify potentially suspicious transactions
SELECT 
    t.id,
    t.transaction_id,
    t.user_id,
    u.username,
    t.type,
    t.amount,
    t.status,
    t.created_at,
    CASE 
        WHEN t.amount > 10000 THEN 'Large Amount'
        WHEN t.amount < 0 THEN 'Negative Amount'
        WHEN t.created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR) AND t.amount > 1000 THEN 'Recent Large Transaction'
        WHEN u.status != 'active' THEN 'Inactive User Transaction'
        ELSE 'Normal'
    END as anomaly_type
FROM transactions t
JOIN users u ON t.user_id = u.id
WHERE (
    t.amount > 10000 
    OR t.amount < 0 
    OR (t.created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR) AND t.amount > 1000)
    OR u.status != 'active'
)
ORDER BY t.created_at DESC, t.amount DESC;

-- 10. COMMISSION CALCULATION VERIFICATION
-- Verify commission calculations are correct
SELECT 
    t.id,
    t.user_id,
    u.username,
    t.product_id,
    p.name as product_name,
    p.price,
    p.commission_rate,
    u.vip_level,
    v.commission_multiplier,
    t.amount as task_amount,
    t.base_commission,
    t.vip_bonus,
    t.commission_earned,
    -- Calculate expected commission
    ROUND(
        (t.amount * p.commission_rate / 100) * v.commission_multiplier, 2
    ) as expected_commission,
    -- Check if calculation is correct
    CASE 
        WHEN ABS(t.commission_earned - ROUND((t.amount * p.commission_rate / 100) * v.commission_multiplier, 2)) < 0.01 
        THEN 'Correct'
        ELSE 'Incorrect'
    END as calculation_status
FROM tasks t
JOIN users u ON t.user_id = u.id
JOIN products p ON t.product_id = p.id
JOIN vip_levels v ON u.vip_level = v.level
WHERE t.status = 'completed'
    AND t.commission_earned > 0
ORDER BY t.completed_at DESC;

-- 11. PLATFORM GROWTH METRICS
-- Track platform growth over time
SELECT 
    DATE_FORMAT(created_at, '%Y-%m') as month,
    COUNT(*) as new_users,
    SUM(COUNT(*)) OVER (ORDER BY DATE_FORMAT(created_at, '%Y-%m')) as cumulative_users,
    AVG(balance) as avg_initial_balance,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users
FROM users
GROUP BY DATE_FORMAT(created_at, '%Y-%m')
ORDER BY month;

-- 12. VIP LEVEL ECONOMICS
-- Analyze the economics of each VIP level
SELECT 
    v.level,
    v.name,
    v.min_balance,
    v.max_daily_tasks,
    v.commission_multiplier,
    v.withdrawal_limit_daily,
    v.withdrawal_fee_percentage,
    COUNT(u.id) as current_users,
    AVG(u.balance) as avg_user_balance,
    SUM(u.total_commission_earned) as total_commission_earned,
    AVG(u.total_commission_earned) as avg_commission_per_user,
    COUNT(t.id) as total_tasks_completed,
    SUM(t.commission_earned) as total_commission_paid
FROM vip_levels v
LEFT JOIN users u ON v.level = u.vip_level AND u.status = 'active'
LEFT JOIN tasks t ON u.id = t.user_id AND t.status = 'completed'
GROUP BY v.level, v.name, v.min_balance, v.max_daily_tasks, v.commission_multiplier, 
         v.withdrawal_limit_daily, v.withdrawal_fee_percentage
ORDER BY v.level;