<?php
/**
 * Bamboo User Application - Personal Information Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: View-only personal information page
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();

// Page configuration
$page_title = 'Personal Information - Kompyte';
$additional_css = [
    BASE_URL . 'user/profile/personal-information/personal-info.css'
];

// Include header
include '../../includes/user_header.php';
?>

<div class="user-container">
    <!-- Hero Section -->
    <div class="personal-info-hero user-fade-in">
        <div class="hero-content">
            <h1 class="hero-title">Personal Information</h1>
            <p class="hero-description">View and manage your personal details and account information</p>
        </div>
    </div>

    <!-- Personal Information Display -->
    <div class="user-card personal-info-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">👤 Profile Details</h5>
            <span class="info-status">
                🔒 Protected
            </span>
        </div>
        <div class="user-card-body">
            


            <!-- Basic Information -->
            <div class="info-section">
                <h6 class="section-title">Basic Information</h6>
                <div class="info-grid">
                    <div class="info-item">
                        <label class="info-label">Username</label>
                        <div class="info-value"><?php echo htmlspecialchars($current_user['username']); ?></div>
                    </div>
                    <div class="info-item">
                        <label class="info-label">Gender</label>
                        <div class="info-value"><?php echo ucfirst($current_user['gender'] ?? 'Not specified'); ?></div>
                    </div>
                    <div class="info-item">
                        <label class="info-label">Full Name</label>
                        <div class="info-value"><?php echo htmlspecialchars($current_user['full_name'] ?? 'Not provided'); ?></div>
                    </div>
                    <div class="info-item">
                        <label class="info-label">Email</label>
                        <div class="info-value"><?php echo htmlspecialchars($current_user['email'] ?? 'Not provided'); ?></div>
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div class="info-section">
                <h6 class="section-title">Account Information</h6>
                <div class="info-grid">
                    <div class="info-item">
                        <label class="info-label">Member Since</label>
                        <div class="info-value"><?php echo date('F j, Y', strtotime($current_user['created_at'])); ?></div>
                    </div>
                    <div class="info-item">
                        <label class="info-label">Account Status</label>
                        <div class="info-value">
                            <span class="status-badge status-active">Active</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <label class="info-label">VIP Level</label>
                        <div class="info-value">VIP <?php echo $current_user['vip_level'] ?? 1; ?></div>
                    </div>
                </div>
            </div>

            <!-- Security Information -->
            <div class="info-section">
                <h6 class="section-title">Security Settings</h6>
                <div class="info-grid">
                    <div class="info-item">
                        <label class="info-label">Login Password</label>
                        <div class="info-value">
                            <span class="security-indicator">🔒 Set</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <label class="info-label">Withdrawal PIN</label>
                        <div class="info-value">
                            <span class="security-indicator">
                                <?php echo !empty($current_user['withdrawal_pin_hash']) ? '🔒 Set' : '⚠️ Not Set'; ?>
                            </span>
                        </div>
                    </div>
                    <div class="info-item">
                        <label class="info-label">Referral Code</label>
                        <div class="info-value">
                            <span class="referral-code"><?php echo htmlspecialchars($current_user['referral_code'] ?? 'Not generated'); ?></span>
                        </div>
                    </div>
                    <div class="info-item">
                        <label class="info-label">Credit Score</label>
                        <div class="info-value">
                            <span class="credit-score"><?php echo $current_user['credit_score'] ?? 0; ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Admin Notice -->
            <div class="admin-contact-notice">
                <div class="notice-icon">📞</div>
                <div class="notice-content">
                    <h6>Need to Update Your Information?</h6>
                    <p>To modify your personal information, please contact our admin team. We'll help you update your details securely.</p>
                    <a href="<?php echo BASE_URL; ?>user/contact/" class="user-btn user-btn-primary">
                        Contact Admin
                    </a>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="<?php echo BASE_URL; ?>user/profile/edit/" class="user-btn user-btn-outline">
                    ✏️ Edit Security Settings
                </a>
                <a href="<?php echo BASE_URL; ?>user/profile/" class="user-btn user-btn-outline">
                    ← Back to Profile
                </a>
            </div>
        </div>
    </div>
</div>

<?php include '../../includes/user_footer.php'; ?>
