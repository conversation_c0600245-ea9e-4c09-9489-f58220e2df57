/**
 * Bamboo User Application - Withdrawal Information Page Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* Page Title */
.withdrawal-info-page-title {
    background: linear-gradient(135deg, var(--user-primary) 0%, var(--user-gradient-end) 100%);
    color: white;
    padding: var(--user-spacing-xl);
    border-radius: var(--user-border-radius-lg);
    margin-bottom: var(--user-spacing-xl);
    text-align: center;
    /* Ensure the header is contained within the user-container */
    margin-left: 0;
    margin-right: 0;
}

.withdrawal-info-page-title .page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-align: center;
}

.withdrawal-info-page-title .page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    text-align: center;
    margin: 0;
}

/* Security Notice */
.security-notice {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.notice-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.notice-text {
    flex: 1;
    font-size: 1rem;
    line-height: 1.5;
}

/* Current Information Card */
.current-info-card .user-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.info-status.complete {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.info-status.incomplete {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.info-item {
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
}

.info-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #64748b;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1rem;
    font-weight: 500;
    color: #1e293b;
    word-break: break-all;
}

.info-value.trc20-address {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    background: #e2e8f0;
    padding: 0.5rem;
    border-radius: 4px;
}

/* Form Styles */
.withdrawal-form {
    max-width: 600px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
}

.label-text {
    font-size: 0.875rem;
}

.required {
    color: #ef4444;
    font-weight: 700;
}

.optional {
    color: #6b7280;
    font-size: 0.75rem;
    font-weight: 400;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control:invalid {
    border-color: #ef4444;
}

.form-help {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
    line-height: 1.4;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

/* Information Points */
.info-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-point {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
}

.point-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.point-text {
    flex: 1;
    line-height: 1.6;
}

.point-text strong {
    color: #1e293b;
    display: block;
    margin-bottom: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .security-notice {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .user-btn-lg {
        width: 100%;
        justify-content: center;
    }
    
    .current-info-card .user-card-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .info-point {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
}

/* Animation Classes */
.user-fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Button Hover Effects */
.user-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.user-btn:active {
    transform: translateY(0);
}

/* Focus States for Accessibility */
.form-control:focus,
.user-btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Loading State */
.form-loading .form-control {
    opacity: 0.6;
    pointer-events: none;
}

.form-loading .user-btn {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.form-loading .user-btn::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Information Display and Modify Functionality */
.info-display {
    margin-bottom: 2rem;
}

.current-info h6 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--user-text-primary);
}

.info-grid {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-item label {
    font-weight: 600;
    color: var(--user-text-secondary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-item span {
    font-size: 1rem;
    color: var(--user-text-primary);
    padding: 0.75rem;
    background: var(--user-bg-secondary);
    border-radius: var(--user-border-radius);
    border: 1px solid var(--user-border-color);
}

.edit-form {
    animation: fadeIn 0.3s ease-in-out;
}

.back-link {
    margin-top: 2rem;
    text-align: center;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .info-grid {
        gap: 1rem;
    }

    .form-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .form-actions .user-btn {
        width: 100%;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}