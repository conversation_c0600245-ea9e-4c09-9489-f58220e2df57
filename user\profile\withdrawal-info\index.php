<?php
/**
 * Bamboo User Application - Withdrawal Information Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: User withdrawal information management
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        $trc20_address = trim($_POST['trc20_address'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $exchange_name = trim($_POST['exchange_name'] ?? '');

        // Validation
        $errors = [];
        
        if (empty($trc20_address)) {
            $errors[] = 'TRC20 address is required.';
        } elseif (strlen($trc20_address) < 34 || !preg_match('/^T[A-Za-z0-9]{33}$/', $trc20_address)) {
            $errors[] = 'Please enter a valid TRC20 address.';
        }
        
        if (empty($phone)) {
            $errors[] = 'Phone number is required.';
        } elseif (!preg_match('/^[\+]?[0-9\s\-\(\)]{10,20}$/', $phone)) {
            $errors[] = 'Please enter a valid phone number.';
        }
        
        if (empty($errors)) {
            // Update user information
            $update_data = [
                'usdt_wallet_address' => $trc20_address,
                'phone' => $phone,
                'exchange_name' => $exchange_name,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            if (updateRecord('users', $update_data, 'id = ?', [$user_id])) {
                $_SESSION['success_message'] = 'Withdrawal information updated successfully!';
                // Refresh current user data
                $current_user = getCurrentUser();
            } else {
                $errors[] = 'Failed to update withdrawal information. Please try again.';
            }
        }
        
        if (!empty($errors)) {
            $_SESSION['error_message'] = implode('<br>', $errors);
        }
    }
}

// Page configuration
$page_title = 'Withdrawal Information';
$page_description = 'Manage your withdrawal information';

// Override CSS and JS paths for this nested directory
$additional_css = [BASE_URL . 'user/profile/withdrawal-info/withdrawal-info.css'];
$additional_js = [BASE_URL . 'user/profile/withdrawal-info/withdrawal-info.js'];

// Include header
include '../../includes/user_header.php';
?>

<div class="user-container">
    <!-- Withdrawal Information Page Title -->
    <div class="withdrawal-info-page-title user-fade-in">
        <div class="user-container">
            <h1 class="page-title">🏦 Withdrawal Information</h1>
            <p class="page-subtitle">Manage your withdrawal details securely</p>
        </div>
    </div>

    <!-- Security Notice -->
    <div class="security-notice user-fade-in">
        <div class="notice-icon">🔒</div>
        <div class="notice-text">
            <strong>Security Notice:</strong> Dear User, to protect the security of your funds, please do not share your password or information with others.
        </div>
    </div>

    <!-- Withdrawal Information Form -->
    <div class="user-card withdrawal-form-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Withdrawal Information</h5>
            <span class="info-status <?php echo (!empty($current_user['usdt_wallet_address'])) ? 'complete' : 'incomplete'; ?>">
                <?php echo (!empty($current_user['usdt_wallet_address'])) ? '✅ Complete' : '⚠️ Incomplete'; ?>
            </span>
        </div>
        <div class="user-card-body">
            <!-- Current Information Display -->
            <div id="info-display" class="info-display">
                <div class="current-info">
                    <h6>Current Withdrawal Information</h6>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>TRC20 Address:</label>
                            <span><?php echo htmlspecialchars($current_user['usdt_wallet_address'] ?? 'Not set'); ?></span>
                        </div>
                        <div class="info-item">
                            <label>Phone Number:</label>
                            <span><?php echo htmlspecialchars($current_user['phone'] ?? 'Not set'); ?></span>
                        </div>
                        <div class="info-item">
                            <label>Exchange Name:</label>
                            <span><?php echo htmlspecialchars($current_user['exchange_name'] ?? 'Not set'); ?></span>
                        </div>
                    </div>
                    <button type="button" id="modify-btn" class="user-btn user-btn-primary">
                        ✏️ Modify Information
                    </button>
                </div>
            </div>

            <!-- Edit Form (Hidden by default) -->
            <div id="edit-form" class="edit-form" style="display: none;">
                <form method="POST" class="withdrawal-form">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <!-- TRC20 Address -->
                <div class="form-group">
                    <label for="trc20_address" class="form-label">
                        <span class="label-text">TRC20 Address</span>
                        <span class="required">*</span>
                    </label>
                    <input type="text"
                           id="trc20_address"
                           name="trc20_address"
                           class="form-control"
                           placeholder="Enter your TRC20 wallet address"
                           value="<?php echo htmlspecialchars($current_user['usdt_wallet_address'] ?? ''); ?>"
                           required>
                    <small class="form-help">Your USDT TRC20 wallet address for withdrawals</small>
                </div>

                <!-- Phone -->
                <div class="form-group">
                    <label for="phone" class="form-label">
                        <span class="label-text">Phone Number</span>
                        <span class="required">*</span>
                    </label>
                    <input type="tel"
                           id="phone"
                           name="phone"
                           class="form-control"
                           placeholder="Enter your phone number"
                           value="<?php echo htmlspecialchars($current_user['phone'] ?? ''); ?>"
                           required>
                    <small class="form-help">Include country code (e.g., +1234567890)</small>
                </div>

                <!-- Exchange Name -->
                <div class="form-group">
                    <label for="exchange_name" class="form-label">
                        <span class="label-text">Exchange Name</span>
                        <span class="optional">(Optional)</span>
                    </label>
                    <input type="text"
                           id="exchange_name"
                           name="exchange_name"
                           class="form-control"
                           placeholder="Enter exchange name (if applicable)"
                           value="<?php echo htmlspecialchars($current_user['exchange_name'] ?? ''); ?>">
                    <small class="form-help">Name of the exchange where your wallet is hosted (optional)</small>
                </div>

                    <!-- Submit Button -->
                    <div class="form-actions">
                        <button type="submit" class="user-btn user-btn-primary user-btn-lg">
                            💾 Update Information
                        </button>
                        <button type="button" id="cancel-btn" class="user-btn user-btn-outline user-btn-lg">
                            ✖️ Cancel
                        </button>
                    </div>
                </form>
            </div>

            <!-- Back to Profile Link -->
            <div class="back-link">
                <a href="<?php echo BASE_URL; ?>user/profile/" class="user-btn user-btn-outline">
                    ← Back to Profile
                </a>
            </div>
        </div>
    </div>


</div>

<!-- Success/Error Notifications -->
<?php if (isset($_SESSION['success_message'])): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof UserApp !== 'undefined' && UserApp.showNotification) {
                UserApp.showNotification('<?php echo addslashes($_SESSION['success_message']); ?>', 'success');
            }
        });
    </script>
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof UserApp !== 'undefined' && UserApp.showNotification) {
                UserApp.showNotification('<?php echo addslashes($_SESSION['error_message']); ?>', 'error');
            }
        });
    </script>
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

<!-- Custom JavaScript for Withdrawal Info -->
<script src="<?php echo BASE_URL; ?>user/profile/withdrawal-info/withdrawal-info.js"></script>

<?php
// Include footer
include '../../includes/user_footer.php';
?>