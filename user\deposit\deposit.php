<?php
/**
 * Bamboo User Application - Deposit Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: User deposit functionality
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Get user balance (stored directly in users table)
$user_balance = [
    'balance' => $current_user['balance'] ?? 0,
    'commission_balance' => $current_user['commission_balance'] ?? 0,
    'total_commission_earned' => $current_user['total_commission_earned'] ?? 0
];

// Handle deposit form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        $amount = floatval($_POST['amount'] ?? 0);
        $selected_contact_id = intval($_POST['contact_id'] ?? 0);

        // Debug: Log the submitted values
        error_log("Deposit form submitted - Amount: $amount, Contact ID: $selected_contact_id");

        if ($amount <= 0) {
            showError('Please enter a valid deposit amount.');
        } elseif ($selected_contact_id <= 0) {
            showError('Please select a customer service contact method.');
        } else {
            // Get the selected contact details
            $selected_contact = fetchRow("SELECT * FROM customer_service_contacts WHERE id = ? AND is_active = 1", [$selected_contact_id]);

            if (!$selected_contact) {
                showError('Invalid customer service contact selected.');
            } else {
                // Create deposit transaction record
                $transaction_id = generateTransactionId('DEP');
                $transaction_data = [
                    'user_id' => $user_id,
                    'type' => 'deposit',
                    'amount' => $amount,
                    'status' => 'pending',
                    'transaction_id' => $transaction_id,
                    'description' => "Deposit request - Amount: $" . number_format($amount, 2) . " via " . $selected_contact['name'],
                    'payment_method' => $selected_contact['name'],
                    'admin_notes' => "Customer service contact: " . $selected_contact['name'],
                    'contact_service_id' => $selected_contact_id // Store the contact ID for later use
                ];

                if (insertRecord('transactions', $transaction_data)) {
                    // Set success flag for JavaScript to handle
                    $deposit_success = true;
                    $redirect_link = $selected_contact['link'];
                    $redirect_service = $selected_contact['name'];

                    // Clear form data after successful submission
                    unset($_POST['amount'], $_POST['contact_id']);
                } else {
                    showError('Failed to submit deposit request. Please try again.');
                }
            }
        }
    }
}

// Get recent deposit transactions with contact service info (limit to 5 for clickable functionality)
$recent_deposits = fetchAll("
    SELECT t.*, csc.name as contact_name, csc.link as contact_link
    FROM transactions t
    LEFT JOIN customer_service_contacts csc ON t.contact_service_id = csc.id
    WHERE t.user_id = ? AND t.type = 'deposit'
    ORDER BY t.created_at DESC
    LIMIT 5
", [$user_id]);

// Get customer service contacts
$customer_service_contacts = fetchAll("SELECT * FROM customer_service_contacts WHERE is_active = 1 ORDER BY sort_order ASC");

// Debug: Log the customer service contacts
error_log("Customer service contacts: " . print_r($customer_service_contacts, true));

// Page configuration
$page_title = 'Deposit Funds';
$page_description = 'Add funds to your account';
$page_css = 'deposit.css';
$page_js = 'deposit.js?v=' . time();

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Deposit Page Title -->
    <div class="deposit-page-title user-fade-in">
        <div class="user-container">
            <h1 class="page-title">💰 Deposit Funds</h1>
            <p class="page-subtitle">Add funds to your account securely</p>
        </div>
    </div>

    <!-- Balance Statistics -->
    <div class="deposit-stats-container user-slide-in">
        <div class="user-row">
            <div class="user-col-6">
                <div class="stat-card balance-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-info">
                        <div class="stat-label">Total Balance</div>
                        <div class="stat-value"><?php echo formatCurrency($user_balance['balance'] ?? 0); ?></div>
                    </div>
                </div>
            </div>
            <div class="user-col-6">
                <div class="stat-card commission-card">
                    <div class="stat-icon">💼</div>
                    <div class="stat-info">
                        <div class="stat-label">Total Commission Earned</div>
                        <div class="stat-value"><?php echo formatCurrency($user_balance['total_commission_earned'] ?? 0); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Important Notice -->
    <div class="deposit-notice user-fade-in">
        <div class="notice-icon">⏰</div>
        <div class="notice-text">
            <strong>Processing Time:</strong> You will receive your deposit within an hour after verification.
        </div>
    </div>

    <!-- Deposit Form -->
    <div class="user-card deposit-form-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Make a Deposit</h5>
            <p class="card-subtitle">Follow the steps below to add funds to your account</p>
        </div>
        <div class="user-card-body">
            <form method="POST" class="deposit-form">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <!-- Step 1: Amount -->
                <div class="form-step">
                    <div class="step-header">
                        <div class="step-number">1</div>
                        <div class="step-title">Enter Deposit Amount</div>
                    </div>
                    <div class="form-group">
                        <label for="amount" class="form-label">Amount (USDT)</label>
                        <input type="number"
                               id="amount"
                               name="amount"
                               class="form-control"
                               placeholder="Enter amount to deposit"
                               min="1"
                               step="0.01"
                               value="<?php echo isset($_POST['amount']) ? htmlspecialchars($_POST['amount']) : ''; ?>"
                               required>
                    </div>
                </div>

                <!-- Step 2: Select Customer Service -->
                <div class="form-step">
                    <div class="step-header">
                        <div class="step-number">2</div>
                        <div class="step-title">Select Customer Service Contact</div>
                    </div>
                    <div class="contact-options">
                        <p class="contact-instruction">Choose a customer service contact method to get the deposit wallet address:</p>
                        <div class="contact-selection">
                            <?php if (!empty($customer_service_contacts)): ?>
                                <div class="form-group">
                                    <label for="contact_id" class="form-label">Customer Service Method</label>
                                    <select name="contact_id" id="contact_id" class="form-select" required>
                                        <option value="">-- Select Contact Method --</option>
                                        <?php foreach ($customer_service_contacts as $contact): ?>
                                            <option value="<?php echo $contact['id']; ?>" <?php echo (isset($_POST['contact_id']) && $_POST['contact_id'] == $contact['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($contact['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <small class="form-help">You will be redirected to the selected contact method after submission</small>
                                </div>
                            <?php else: ?>
                                <div class="no-contacts">
                                    <p>Customer service contacts will be available soon.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="form-actions">
                    <button type="submit" class="user-btn user-btn-primary user-btn-lg">
                        📤 Submit Deposit Request
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Redirect Progress Modal -->
    <div id="redirectModal" class="redirect-modal" style="display: none;">
        <div class="redirect-modal-content">
            <div class="redirect-header">
                <h3>🚀 Redirecting to Customer Service</h3>
                <p id="redirectServiceName">Telegram Support</p>
            </div>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text">
                    <span id="progressPercent">0%</span>
                    <span>Opening in <span id="countdown">3</span> seconds...</span>
                </div>
            </div>
            <div class="redirect-info">
                <p>✅ Deposit request submitted successfully</p>
                <p>📞 Opening customer service contact</p>
                <p>🔄 You'll be redirected back to this page</p>
            </div>
        </div>
    </div>

    <!-- Deposit History -->
    <div class="user-card deposit-history-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Recent Deposits</h5>
            <a href="<?php echo BASE_URL; ?>user/transactions/" class="user-btn user-btn-sm user-btn-outline">View All</a>
        </div>
        <div class="user-card-body">
            <?php if (!empty($recent_deposits)): ?>
                <div class="deposit-table">
                    <?php foreach ($recent_deposits as $deposit): ?>
                        <?php
                        // Only make pending transactions with contact links clickable
                        $is_clickable = !empty($deposit['contact_link']) && $deposit['status'] === 'pending';
                        ?>
                        <div class="deposit-row <?php echo $is_clickable ? 'clickable-deposit' : ''; ?>"
                             <?php if ($is_clickable): ?>
                                 data-contact-link="<?php echo htmlspecialchars($deposit['contact_link']); ?>"
                                 data-contact-name="<?php echo htmlspecialchars($deposit['contact_name'] ?? $deposit['payment_method']); ?>"
                                 title="Click to contact <?php echo htmlspecialchars($deposit['contact_name'] ?? $deposit['payment_method']); ?>"
                             <?php endif; ?>>
                            <div class="deposit-info">
                                <div class="deposit-icon">💰</div>
                                <div class="deposit-details">
                                    <div class="deposit-amount">+<?php echo formatCurrency($deposit['amount']); ?></div>
                                    <div class="deposit-date"><?php echo formatDate($deposit['created_at']); ?></div>
                                    <?php if (!empty($deposit['transaction_id'])): ?>
                                        <div class="deposit-txn-id">ID: <?php echo htmlspecialchars($deposit['transaction_id']); ?></div>
                                    <?php endif; ?>
                                    <?php if (!empty($deposit['contact_name']) || !empty($deposit['payment_method'])): ?>
                                        <div class="deposit-method">
                                            via <?php echo htmlspecialchars($deposit['contact_name'] ?? $deposit['payment_method']); ?>
                                            <?php if ($is_clickable): ?>
                                                <span class="contact-hint">📞 Click to contact</span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="deposit-status">
                                <span class="status-badge status-<?php echo $deposit['status']; ?>">
                                    <?php echo ucfirst($deposit['status']); ?>
                                </span>
                                <?php if ($is_clickable): ?>
                                    <div class="contact-action">
                                        <i class="icon-external-link"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-deposits">
                    <div class="no-data-icon">💰</div>
                    <h6>No Deposits Yet</h6>
                    <p>Your deposit history will appear here once you make your first deposit.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Success/Error Notifications -->
<?php if (isset($_SESSION['success_message'])): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            UserApp.showNotification('<?php echo addslashes($_SESSION['success_message']); ?>', 'success');
        });
    </script>
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            UserApp.showNotification('<?php echo addslashes($_SESSION['error_message']); ?>', 'error');
        });
    </script>
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

<!-- Handle successful deposit submission -->
<?php if (isset($deposit_success) && $deposit_success): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Show success notification
            UserApp.showNotification('Deposit request submitted successfully! Redirecting to customer service...', 'success');

            // Start redirect progress after a short delay
            setTimeout(function() {
                showRedirectProgress('<?php echo addslashes($redirect_link); ?>', '<?php echo addslashes($redirect_service); ?>');
            }, 1000);
        });
    </script>
<?php endif; ?>

<?php
// Include footer
include '../includes/user_footer.php';
?>
