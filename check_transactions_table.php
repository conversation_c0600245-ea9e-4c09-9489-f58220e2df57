<?php
define('BAMBOO_APP', true);
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check table structure
echo "Checking transactions table structure:\n";
$columns = fetchAll("DESCRIBE transactions");
foreach ($columns as $column) {
    echo "Column: " . $column['Field'] . " - Type: " . $column['Type'] . " - Null: " . $column['Null'] . " - Default: " . $column['Default'] . "\n";
}

echo "\nChecking recent transactions:\n";
$transactions = fetchAll("SELECT * FROM transactions ORDER BY created_at DESC LIMIT 5");
foreach ($transactions as $transaction) {
    echo "ID: " . $transaction['id'] . " - User: " . $transaction['user_id'] . " - Type: " . $transaction['type'] . " - Amount: " . $transaction['amount'] . " - Status: " . $transaction['status'] . "\n";
}
?>
