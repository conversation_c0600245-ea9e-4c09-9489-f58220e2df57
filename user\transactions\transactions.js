/**
 * Bamboo User Dashboard - Transactions Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 * Description: Interactive functionality for the transactions page
 */

// Global variables
let transactionModal = null;
let currentTransactionId = null;

// Initialize page when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeTransactionsPage();
});

/**
 * Initialize the transactions page
 */
function initializeTransactionsPage() {
    // Initialize modal
    initializeModal();
    
    // Initialize filter form
    initializeFilters();
    
    // Initialize table interactions
    initializeTableInteractions();
    
    // Initialize quick actions
    initializeQuickActions();
    
    // Load initial data if needed
    loadTransactionData();
    
    console.log('Transactions page initialized successfully');
}

/**
 * Initialize the transaction details modal
 */
function initializeModal() {
    transactionModal = document.getElementById('transactionDetailsModal');
    
    if (transactionModal) {
        // Close modal when clicking the X button
        const closeBtn = transactionModal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', closeTransactionDetails);
        }
        
        // Close modal when clicking outside
        transactionModal.addEventListener('click', function(e) {
            if (e.target === transactionModal) {
                closeTransactionDetails();
            }
        });
        
        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && transactionModal.style.display === 'block') {
                closeTransactionDetails();
            }
        });
    }
}

/**
 * Initialize filter form functionality
 */
function initializeFilters() {
    const filterForm = document.querySelector('.filter-form');
    
    if (filterForm) {
        // Auto-submit form when filters change
        const filterInputs = filterForm.querySelectorAll('select, input[type="date"]');
        
        filterInputs.forEach(input => {
            input.addEventListener('change', function() {
                // Add a small delay to allow multiple quick changes
                clearTimeout(this.filterTimeout);
                this.filterTimeout = setTimeout(() => {
                    filterForm.submit();
                }, 500);
            });
        });
        
        // Clear filters functionality
        const clearBtn = filterForm.querySelector('a[href="?"]');
        if (clearBtn) {
            clearBtn.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Reset all form fields
                filterInputs.forEach(input => {
                    if (input.type === 'date') {
                        input.value = '';
                    } else {
                        input.selectedIndex = 0;
                    }
                });
                
                // Submit the cleared form
                setTimeout(() => {
                    window.location.href = window.location.pathname;
                }, 100);
            });
        }
    }
}

/**
 * Initialize table interactions
 */
function initializeTableInteractions() {
    // Add hover effects and click handlers for transaction rows
    const transactionRows = document.querySelectorAll('.transaction-row');
    
    transactionRows.forEach(row => {
        // Add click handler for row selection
        row.addEventListener('click', function(e) {
            // Don't trigger if clicking on a button or link
            if (e.target.tagName === 'BUTTON' || e.target.tagName === 'A' || e.target.closest('button')) {
                return;
            }
            
            // Toggle row selection
            row.classList.toggle('selected');
        });
    });
    
    // Initialize details buttons
    const detailsBtns = document.querySelectorAll('.details-btn');
    detailsBtns.forEach(btn => {
        // Remove any existing event listeners to avoid conflicts with onclick
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            // The onclick attribute will handle the showTransactionDetails call
            // This listener just prevents event bubbling
        });
    });
}

/**
 * Initialize quick actions
 */
function initializeQuickActions() {
    const actionBtns = document.querySelectorAll('.action-btn');
    
    actionBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            // Add loading state
            this.style.opacity = '0.7';
            this.style.pointerEvents = 'none';
            
            // Reset after navigation
            setTimeout(() => {
                this.style.opacity = '';
                this.style.pointerEvents = '';
            }, 1000);
        });
    });
}

/**
 * Load transaction data (for dynamic updates)
 */
function loadTransactionData() {
    // This function can be used to periodically refresh transaction data
    // or load additional data as needed
    
    // Example: Auto-refresh every 5 minutes
    setInterval(() => {
        refreshTransactionSummary();
    }, 5 * 60 * 1000);
}

/**
 * Show transaction details in modal
 */
function showTransactionDetails(transactionId) {
    if (!transactionModal) {
        console.error('Transaction modal not found');
        return;
    }
    
    currentTransactionId = transactionId;
    const modalContent = document.getElementById('transactionDetailsContent');
    
    if (!modalContent) {
        console.error('Modal content container not found');
        return;
    }
    
    // Show loading state
    modalContent.innerHTML = `
        <div class="loading-state">
            <div class="loading-spinner"></div>
            <p>Loading transaction details...</p>
        </div>
    `;
    
    // Show modal
    transactionModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // Fetch transaction details
    fetch(`../api/transactions.php?action=details&id=${transactionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTransactionDetails(data.data);
            } else {
                throw new Error(data.message || 'Failed to load transaction details');
            }
        })
        .catch(error => {
            console.error('Error loading transaction details:', error);
            modalContent.innerHTML = `
                <div class="error-state">
                    <div class="error-icon">⚠️</div>
                    <h4>Error Loading Details</h4>
                    <p>Unable to load transaction details. Please try again.</p>
                    <button class="user-btn user-btn-primary" onclick="showTransactionDetails(${transactionId})">
                        Retry
                    </button>
                </div>
            `;
        });
}

/**
 * Display transaction details in modal
 */
function displayTransactionDetails(transaction) {
    const modalContent = document.getElementById('transactionDetailsContent');
    
    if (!modalContent) return;
    
    const detailsHtml = `
        <div class="transaction-details">
            <div class="details-header">
                <div class="transaction-type">
                    <i class="icon-${transaction.type}"></i>
                    <span>${transaction.display_type}</span>
                </div>
                <div class="transaction-status">
                    <span class="status-badge status-${transaction.status}">
                        ${transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                    </span>
                </div>
            </div>
            
            <div class="details-grid">
                <div class="detail-item">
                    <label>Transaction ID</label>
                    <value>${transaction.transaction_id || transaction.order_no || 'N/A'}</value>
                </div>
                
                <div class="detail-item">
                    <label>Amount</label>
                    <value class="amount ${transaction.amount >= 0 ? 'positive' : 'negative'}">
                        ${transaction.amount >= 0 ? '+' : ''}${formatCurrency(transaction.amount)}
                    </value>
                </div>
                
                <div class="detail-item">
                    <label>Date & Time</label>
                    <value>${formatDateTime(transaction.created_at)}</value>
                </div>
                
                <div class="detail-item">
                    <label>Balance Before</label>
                    <value>${formatCurrency(transaction.balance_before || 0)}</value>
                </div>
                
                <div class="detail-item">
                    <label>Balance After</label>
                    <value>${formatCurrency(transaction.balance_after || 0)}</value>
                </div>
                
                ${transaction.description ? `
                <div class="detail-item full-width">
                    <label>Description</label>
                    <value>${transaction.description}</value>
                </div>
                ` : ''}
                
                ${transaction.reference_id ? `
                <div class="detail-item">
                    <label>Reference ID</label>
                    <value>${transaction.reference_id}</value>
                </div>
                ` : ''}
                
                ${transaction.payment_method ? `
                <div class="detail-item">
                    <label>Payment Method</label>
                    <value>${transaction.payment_method}</value>
                </div>
                ` : ''}
                
                ${transaction.fee ? `
                <div class="detail-item">
                    <label>Fee</label>
                    <value>${formatCurrency(transaction.fee)}</value>
                </div>
                ` : ''}
            </div>
            
            ${transaction.notes ? `
            <div class="transaction-notes">
                <h4>Notes</h4>
                <p>${transaction.notes}</p>
            </div>
            ` : ''}
        </div>
    `;
    
    modalContent.innerHTML = detailsHtml;
}

/**
 * Close transaction details modal
 */
function closeTransactionDetails() {
    if (transactionModal) {
        transactionModal.style.display = 'none';
        document.body.style.overflow = '';
        currentTransactionId = null;
    }
}

/**
 * Refresh transaction summary data
 */
function refreshTransactionSummary() {
    fetch('../api/transactions.php?action=summary')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateSummaryCards(data.data);
            }
        })
        .catch(error => {
            console.error('Error refreshing summary:', error);
        });
}

/**
 * Update summary cards with new data
 */
function updateSummaryCards(summaryData) {
    const cards = {
        deposits: document.querySelector('.deposits-card .amount'),
        withdrawals: document.querySelector('.withdrawals-card .amount'),
        commissions: document.querySelector('.commissions-card .amount')
    };
    
    if (cards.deposits && summaryData.total_deposits_formatted) {
        cards.deposits.textContent = `USDT ${summaryData.total_deposits_formatted}`;
    }
    
    if (cards.withdrawals && summaryData.total_withdrawals_formatted) {
        cards.withdrawals.textContent = `USDT ${summaryData.total_withdrawals_formatted}`;
    }
    
    if (cards.commissions && summaryData.total_commissions_formatted) {
        cards.commissions.textContent = `USDT ${summaryData.total_commissions_formatted}`;
    }
}

/**
 * Format currency amount
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

/**
 * Format date and time
 */
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

/**
 * Export transactions data
 */
function exportTransactions(format = 'csv') {
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('export', format);
    
    // Create a temporary link to trigger download
    const link = document.createElement('a');
    link.href = currentUrl.toString();
    link.download = `transactions_${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * Print transactions
 */
function printTransactions() {
    window.print();
}

/**
 * Global function for inline onclick handlers (backward compatibility)
 */
window.showTransactionDetails = showTransactionDetails;
window.closeTransactionDetails = closeTransactionDetails;
window.exportTransactions = exportTransactions;
window.printTransactions = printTransactions;