<?php
define('BAMBOO_APP', true);
require_once 'includes/config.php';
require_once 'includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h2>Admin Recharge Functionality Test</h2>";

// Test 1: Check admin login status
echo "<h3>1. Admin Login Status</h3>";
if (isAdminLoggedIn()) {
    echo "✅ Admin is logged in<br>";
    echo "Admin ID: " . ($_SESSION['admin_id'] ?? 'Not set') . "<br>";
    echo "User Type: " . ($_SESSION['user_type'] ?? 'Not set') . "<br>";
    echo "Admin Username: " . ($_SESSION['admin_username'] ?? 'Not set') . "<br>";
} else {
    echo "❌ Admin is NOT logged in<br>";
    echo "<p style='color: red;'><strong>ISSUE FOUND:</strong> Admin must be logged in to approve/reject transactions.</p>";
    echo "<p><a href='admin/login/' target='_blank'>Click here to login to admin panel</a></p>";
    echo "Session data: <pre>" . print_r($_SESSION, true) . "</pre>";
}

// Test 2: Check if there are pending deposits
echo "<h3>2. Pending Deposit Transactions</h3>";
try {
    $pending_deposits = fetchAll("
        SELECT t.id, t.user_id, t.amount, t.status, t.created_at, u.username 
        FROM transactions t 
        LEFT JOIN users u ON t.user_id = u.id 
        WHERE t.type = 'deposit' AND t.status = 'pending' 
        ORDER BY t.created_at DESC 
        LIMIT 10
    ");
    
    if (!empty($pending_deposits)) {
        echo "✅ Found " . count($pending_deposits) . " pending deposit transactions:<br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Status</th><th>Created</th></tr>";
        foreach ($pending_deposits as $txn) {
            echo "<tr>";
            echo "<td>{$txn['id']}</td>";
            echo "<td>{$txn['username']}</td>";
            echo "<td>\${$txn['amount']}</td>";
            echo "<td>{$txn['status']}</td>";
            echo "<td>{$txn['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "ℹ️ No pending deposit transactions found.<br>";
        echo "<p>To test the approval functionality, you need to create a pending deposit transaction first.</p>";
    }
} catch (Exception $e) {
    echo "❌ Error fetching pending deposits: " . $e->getMessage() . "<br>";
}

// Test 3: Create a test pending deposit if none exist
if (empty($pending_deposits)) {
    echo "<h3>3. Creating Test Deposit Transaction</h3>";
    try {
        // Get a test user
        $test_user = fetchRow("SELECT id, username, balance FROM users ORDER BY id LIMIT 1");
        if ($test_user) {
            // Create a test deposit transaction
            $test_amount = 100.00;
            $insert_data = [
                'user_id' => $test_user['id'],
                'type' => 'deposit',
                'amount' => $test_amount,
                'balance_before' => $test_user['balance'],
                'balance_after' => $test_user['balance'], // Will be updated on approval
                'status' => 'pending',
                'payment_method' => 'Test Payment',
                'transaction_id' => 'TEST_' . time(),
                'description' => 'Test deposit for admin approval testing'
            ];
            
            $transaction_id = insertRecord('transactions', $insert_data);
            if ($transaction_id) {
                echo "✅ Created test deposit transaction with ID: $transaction_id<br>";
                echo "User: {$test_user['username']}, Amount: \$$test_amount<br>";
                
                // Refresh the pending deposits list
                $pending_deposits = fetchAll("
                    SELECT t.id, t.user_id, t.amount, t.status, t.created_at, u.username 
                    FROM transactions t 
                    LEFT JOIN users u ON t.user_id = u.id 
                    WHERE t.type = 'deposit' AND t.status = 'pending' 
                    ORDER BY t.created_at DESC 
                    LIMIT 10
                ");
            } else {
                echo "❌ Failed to create test deposit transaction<br>";
            }
        } else {
            echo "❌ No users found to create test transaction<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error creating test deposit: " . $e->getMessage() . "<br>";
    }
}

// Test 4: Test approval functionality (if admin is logged in and there are pending deposits)
if (isAdminLoggedIn() && !empty($pending_deposits)) {
    echo "<h3>4. Testing Approval Functionality</h3>";
    $test_transaction = $pending_deposits[0];
    
    echo "<p>Testing approval for Transaction ID: {$test_transaction['id']}</p>";
    
    try {
        // Get the transaction details
        $transaction = fetchRow('SELECT * FROM transactions WHERE id = ?', [$test_transaction['id']]);
        if ($transaction && $transaction['status'] === 'pending') {
            echo "✅ Transaction found and is pending<br>";
            
            // Get user details
            $user = fetchRow('SELECT id, balance FROM users WHERE id = ?', [$transaction['user_id']]);
            if ($user) {
                echo "✅ User found, current balance: \${$user['balance']}<br>";
                
                $old_balance = $user['balance'];
                $new_balance = $old_balance + $transaction['amount'];
                
                echo "Amount to add: \${$transaction['amount']}<br>";
                echo "New balance would be: \$$new_balance<br>";
                
                // Simulate the approval process (without actually executing it)
                echo "<p style='color: green;'><strong>Approval simulation successful!</strong></p>";
                echo "<p>The approval process would:</p>";
                echo "<ul>";
                echo "<li>Update user balance from \$$old_balance to \$$new_balance</li>";
                echo "<li>Update transaction status from 'pending' to 'completed'</li>";
                echo "<li>Set processed_by to admin ID: " . $_SESSION['admin_id'] . "</li>";
                echo "<li>Set processed_at timestamp</li>";
                echo "</ul>";
                
            } else {
                echo "❌ User not found for transaction<br>";
            }
        } else {
            echo "❌ Transaction not found or not pending<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error testing approval: " . $e->getMessage() . "<br>";
    }
}

// Test 5: Check for common issues
echo "<h3>5. Common Issues Check</h3>";

// Check CSRF token functionality
try {
    $csrf_token = generateCSRFToken();
    if (verifyCSRFToken($csrf_token)) {
        echo "✅ CSRF token generation and verification working<br>";
    } else {
        echo "❌ CSRF token verification failed<br>";
    }
} catch (Exception $e) {
    echo "❌ CSRF token error: " . $e->getMessage() . "<br>";
}

// Check database functions
try {
    $test_query = fetchRow("SELECT COUNT(*) as count FROM transactions");
    echo "✅ Database connection and query functions working<br>";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Check updateRecord function
try {
    // Test with a harmless update that doesn't change anything
    $result = updateRecord('transactions', ['updated_at' => date('Y-m-d H:i:s')], 'id = ?', [999999]);
    echo "✅ updateRecord function is working (even if no rows affected)<br>";
} catch (Exception $e) {
    echo "❌ updateRecord function error: " . $e->getMessage() . "<br>";
}

echo "<h3>6. Next Steps</h3>";
if (!isAdminLoggedIn()) {
    echo "<p style='color: red;'><strong>MAIN ISSUE:</strong> Admin is not logged in.</p>";
    echo "<p><strong>Solution:</strong> <a href='admin/login/' target='_blank'>Login to admin panel first</a></p>";
} else {
    echo "<p style='color: green;'>Admin is logged in. The approval/rejection functionality should work.</p>";
    echo "<p><strong>To test:</strong></p>";
    echo "<ol>";
    echo "<li>Go to <a href='admin/recharge/' target='_blank'>admin/recharge/</a></li>";
    echo "<li>Try to approve or reject a pending transaction</li>";
    echo "<li>Check browser console for JavaScript errors</li>";
    echo "<li>Check server error logs for any PHP errors</li>";
    echo "</ol>";
}

?>