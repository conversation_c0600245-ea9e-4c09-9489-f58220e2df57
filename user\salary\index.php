<?php
/**
 * Bamboo User Application - Salary Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: User salary transaction history
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Get salary transactions for the user
$salaries = fetchAll('SELECT * FROM user_salaries WHERE user_id = ? ORDER BY created_at DESC', [$user_id]);

// Calculate total salary received
$total_salary = fetchRow("SELECT SUM(amount) as total FROM user_salaries WHERE user_id = ? AND status = 'paid'", [$user_id]);
$total_salary_amount = $total_salary ? $total_salary['total'] : 0;

// Get salary statistics
$total_payments = count($salaries);
$pending_payments = count(array_filter($salaries, function($s) { return $s['status'] === 'pending_approval'; }));
$paid_payments = count(array_filter($salaries, function($s) { return $s['status'] === 'paid'; }));

// Page configuration
$page_title = 'Salary History - Kompyte';
$additional_css = [
    BASE_URL . 'user/salary/salary.css'
];

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Page Header -->
    <div class="salary-page-title user-fade-in">
        <div class="title-content">
            <h1 class="page-title">💼 Salary History</h1>
            <p class="page-subtitle">View your salary payments and transaction history</p>
        </div>
    </div>

    <!-- Salary Statistics -->
    <div class="salary-stats-grid user-fade-in">
        <div class="stat-card total-salary">
            <div class="stat-icon">💰</div>
            <div class="stat-content">
                <h3 class="stat-value"><?php echo formatCurrency($total_salary_amount); ?></h3>
                <p class="stat-label">Total Salary Received</p>
            </div>
        </div>
        <div class="stat-card total-payments">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
                <h3 class="stat-value"><?php echo $total_payments; ?></h3>
                <p class="stat-label">Total Payments</p>
            </div>
        </div>
        <div class="stat-card paid-payments">
            <div class="stat-icon">✅</div>
            <div class="stat-content">
                <h3 class="stat-value"><?php echo $paid_payments; ?></h3>
                <p class="stat-label">Paid</p>
            </div>
        </div>
        <div class="stat-card pending-payments">
            <div class="stat-icon">⏳</div>
            <div class="stat-content">
                <h3 class="stat-value"><?php echo $pending_payments; ?></h3>
                <p class="stat-label">Pending</p>
            </div>
        </div>
    </div>

    <!-- Salary History -->
    <div class="user-card salary-history-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Salary Transaction History</h5>
            <span class="history-count"><?php echo $total_payments; ?> transactions</span>
        </div>
        <div class="user-card-body">
            <?php if (empty($salaries)): ?>
                <div class="no-salary-data">
                    <div class="no-data-icon">💼</div>
                    <h6>No Salary Records</h6>
                    <p>You haven't received any salary payments yet. Salary payments will appear here once processed by the admin.</p>
                </div>
            <?php else: ?>
                <div class="salary-transactions">
                    <?php foreach ($salaries as $index => $salary): ?>
                        <div class="salary-transaction-item">
                            <div class="transaction-info">
                                <div class="transaction-icon">
                                    <?php echo $salary['status'] === 'paid' ? '💰' : '⏳'; ?>
                                </div>
                                <div class="transaction-details">
                                    <div class="transaction-title">Salary Payment #<?php echo $total_payments - $index; ?></div>
                                    <div class="transaction-date"><?php echo date('M j, Y H:i', strtotime($salary['created_at'])); ?></div>
                                    <?php if (!empty($salary['notes'])): ?>
                                        <div class="transaction-notes"><?php echo htmlspecialchars($salary['notes']); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="transaction-amount">
                                <span class="amount-value"><?php echo formatCurrency($salary['amount']); ?></span>
                            </div>
                            <div class="transaction-status">
                                <span class="status-badge status-<?php echo $salary['status']; ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $salary['status'])); ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Back to Profile -->
    <div class="back-to-profile user-fade-in">
        <a href="<?php echo BASE_URL; ?>user/profile/" class="user-btn user-btn-outline">
            ← Back to Profile
        </a>
    </div>
</div>

<?php include '../includes/user_footer.php'; ?>
