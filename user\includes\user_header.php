<?php
/**
 * Bamboo User Dashboard - Universal Header
 * Company: Notepadsly
 * Version: 1.0
 * Description: Reusable header component for all user pages
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

// Ensure user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get user information
$current_user = getCurrentUser();
$user_balance = getUserBalance($current_user['id']);
$user_vip = getUserVipLevel($current_user['id']);
$unread_notifications = getUnreadNotificationsCount($current_user['id']);

// Get appearance settings for dynamic theming
$appearance_settings = [
    'primary_color' => getAppSetting('appearance_primary_color', '#ff6900'),
    'secondary_color' => getAppSetting('appearance_secondary_color', '#ffffff'),
    'accent_color' => getAppSetting('appearance_accent_color', '#007bff'),
    'gradient_start' => getAppSetting('appearance_gradient_start', '#ff6900'),
    'gradient_end' => getAppSetting('appearance_gradient_end', '#ff8533'),
];

// Get current page for navigation highlighting
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$current_dir = basename(dirname($_SERVER['PHP_SELF']));
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title ?? 'Dashboard'); ?> - <?php echo htmlspecialchars(getAppSetting('app_name', APP_NAME)); ?></title>
    
    <!-- Favicon -->
    <?php
    $favicon_path = ASSETS_URL . 'images/favicon.ico';
    $favicon_exists = file_exists(__DIR__ . '/../../assets/images/favicon.ico') && filesize(__DIR__ . '/../../assets/images/favicon.ico') > 100; // Check if it's a real file, not placeholder
    ?>
    <?php if ($favicon_exists): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo $favicon_path; ?>?v=<?php echo filemtime(__DIR__ . '/../../assets/images/favicon.ico'); ?>">
        <link rel="shortcut icon" type="image/x-icon" href="<?php echo $favicon_path; ?>?v=<?php echo filemtime(__DIR__ . '/../../assets/images/favicon.ico'); ?>">
    <?php else: ?>
        <!-- Fallback: Use a data URI for a simple favicon -->
        <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><rect width='32' height='32' fill='%23ff6900'/><text x='16' y='20' font-family='Arial' font-size='18' fill='white' text-anchor='middle'>K</text></svg>">
    <?php endif; ?>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- CSS Files -->
    <link href="<?php echo BASE_URL; ?>user/assets/css/user-master.css" rel="stylesheet">
    <?php if (isset($page_css)): ?>
        <link href="<?php echo BASE_URL; ?>user/<?php echo $current_dir; ?>/<?php echo $page_css; ?>" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Additional CSS -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Meta tags -->
    <meta name="description" content="<?php echo htmlspecialchars($page_description ?? 'User Dashboard'); ?>">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="<?php echo BASE_URL; ?>user/assets/css/user-master.css" as="style">
</head>
<body class="user-dashboard">
    <!-- Loading Overlay -->
    <div id="user-loading-overlay" class="user-loading-overlay" style="display: none;">
        <div class="user-loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">Loading...</div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="user-notifications" class="user-notifications"></div>

    <!-- Header -->
    <header class="user-header">
        <div class="user-container-fluid">
            <div class="user-header-content">
                <!-- Logo Section -->
                <div class="user-header-logo">
                    <a href="<?php echo BASE_URL; ?>user/dashboard/" class="logo-link">
                        <span class="logo-text">Kompyte</span>
                    </a>
                </div>

                <!-- Navigation Menu -->
                <nav class="user-nav">
                    <ul class="user-nav-list">
                        <li class="user-nav-item">
                            <a href="<?php echo BASE_URL; ?>user/dashboard/" class="user-nav-link">
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="user-nav-item">
                            <a href="<?php echo BASE_URL; ?>user/tasks/" class="user-nav-link">
                                <span>Tasks</span>
                            </a>
                        </li>
                        <li class="user-nav-item">
                            <a href="<?php echo BASE_URL; ?>user/records/" class="user-nav-link">
                                <span>Records</span>
                            </a>
                        </li>
                        <li class="user-nav-item">
                            <a href="<?php echo BASE_URL; ?>user/profile/" class="user-nav-link">
                                <span>Profile</span>
                            </a>
                        </li>
                    </ul>
                </nav>

                <!-- User Info Section -->
                <div class="user-header-info">
                    <!-- Balance Display -->
                    <div class="user-balance-display">
                        <span class="balance-value"><?php echo formatCurrency($user_balance['balance'] ?? 0); ?></span>
                    </div>

                    <!-- User Profile Display -->
                    <div class="user-profile-display">
                        <div class="user-avatar">
                            <?php if (!empty($current_user['avatar'])): ?>
                                <img src="<?php echo BASE_URL . 'uploads/avatars/' . htmlspecialchars($current_user['avatar']); ?>" alt="Avatar" class="avatar-img">
                            <?php else: ?>
                                <div class="avatar-placeholder">
                                    <?php echo strtoupper(substr($current_user['username'], 0, 1)); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="user-info">
                            <div class="username"><?php echo htmlspecialchars($current_user['username']); ?></div>
                            <div class="vip-level">VIP <?php echo $user_vip['level'] ?? 1; ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content Wrapper -->
    <main class="user-main-content">
        
        <!-- Global JavaScript Variables -->
        <script>
            // Initialize UserApp configuration
            window.UserApp = window.UserApp || {};
            
            // Set configuration from PHP
            Object.assign(window.UserApp, {
                config: {
                    baseUrl: '<?php echo BASE_URL; ?>',
                    assetsUrl: '<?php echo ASSETS_URL; ?>',
                    csrfToken: '<?php echo generateCSRFToken(); ?>',
                    userId: <?php echo $current_user['id']; ?>,
                    username: '<?php echo htmlspecialchars($current_user['username']); ?>',
                    vipLevel: <?php echo $user_vip['level'] ?? 1; ?>,
                    balance: <?php echo $user_balance['balance'] ?? 0; ?>,
                    commissionBalance: <?php echo $user_balance['commission_balance'] ?? 0; ?>
                },
                appearanceSettings: <?php echo json_encode($appearance_settings); ?>
            });
        </script>
