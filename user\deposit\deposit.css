/**
 * Bamboo User Application - Deposit Page Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* ===== HEADER BALANCE FIX ===== */
.user-balance-display .balance-value {
    font-size: var(--user-font-size) !important;
    color: var(--user-success) !important;
}

/* ===== DEPOSIT PAGE TITLE ===== */
.deposit-page-title {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    padding: var(--user-spacing-xl) 0;
    border-radius: var(--user-border-radius-lg);
    margin-bottom: var(--user-spacing-xl);
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 var(--user-spacing-sm) 0;
    text-align: center;
}

.page-subtitle {
    font-size: var(--user-font-size-lg);
    opacity: 0.9;
    text-align: center;
    margin: 0;
}

/* ===== DEPOSIT STATISTICS ===== */
.deposit-stats-container {
    margin-bottom: var(--user-spacing-xl);
}

/* Fix spacing between stat cards */
.deposit-stats-container .user-row {
    margin-left: 0;
    margin-right: 0;
}

.deposit-stats-container .user-col-6 {
    padding-left: calc(var(--user-spacing-sm) / 2);
    padding-right: calc(var(--user-spacing-sm) / 2);
}

.deposit-stats-container .user-col-6:first-child {
    padding-left: 0;
}

.deposit-stats-container .user-col-6:last-child {
    padding-right: 0;
}

.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.9));
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--user-border-radius-lg);
    padding: var(--user-spacing-xl);
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: var(--user-shadow);
    transition: var(--user-transition);
    margin-bottom: 0;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--user-shadow-lg);
}

.balance-card {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.05));
    border-color: rgba(40, 167, 69, 0.2);
}

.commission-card {
    background: linear-gradient(135deg, rgba(255, 105, 0, 0.1), rgba(255, 133, 51, 0.05));
    border-color: rgba(255, 105, 0, 0.2);
}

.stat-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.stat-info {
    flex: 1;
}

.stat-label {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
    margin-bottom: var(--user-spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--user-primary);
}

/* ===== DEPOSIT NOTICE ===== */
.deposit-notice {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border: 2px solid #f59e0b;
    border-radius: var(--user-border-radius);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 2rem;
}

.notice-icon {
    font-size: 1.5rem;
    color: #d97706;
}

.notice-text {
    color: #92400e;
    font-weight: 500;
}

/* ===== DEPOSIT FORM ===== */
.deposit-form-card {
    margin-bottom: 2rem;
}

.card-subtitle {
    color: var(--user-text-secondary);
    margin: 0;
    font-size: 0.875rem;
}

.deposit-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* ===== FORM STEPS ===== */
.form-step {
    border: 2px solid #e5e7eb;
    border-radius: var(--user-border-radius);
    padding: 1.5rem;
    background: #f8fafc;
}

.step-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.125rem;
}

.step-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--user-text-dark);
}

/* ===== CONTACT OPTIONS ===== */
.contact-instruction {
    color: var(--user-text-secondary);
    margin-bottom: 1rem;
}

.contact-selection {
    max-width: 400px;
}

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--user-text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--user-border-color);
    border-radius: var(--user-border-radius);
    background: var(--user-card-bg);
    color: var(--user-text-primary);
    font-size: 1rem;
    transition: var(--user-transition);
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1rem;
    padding-right: 3rem;
}

.form-select:focus {
    outline: none;
    border-color: var(--user-primary);
    box-shadow: 0 0 0 3px rgba(var(--user-primary-rgb), 0.1);
}

.form-select:hover {
    border-color: var(--user-primary);
}

.form-help {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: var(--user-text-secondary);
    font-style: italic;
}



/* Redirect Progress Modal */
.redirect-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

.redirect-modal-content {
    background: white;
    padding: 2rem;
    border-radius: var(--user-border-radius-lg);
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.redirect-header h3 {
    margin: 0 0 0.5rem 0;
    color: var(--user-primary);
    font-size: 1.25rem;
}

.redirect-header p {
    margin: 0 0 1.5rem 0;
    color: var(--user-text-muted);
    font-weight: 600;
}

.progress-container {
    margin-bottom: 1.5rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.75rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--user-primary), var(--user-success));
    border-radius: 4px;
    transition: width 0.1s ease;
    width: 0%;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: var(--user-text-muted);
}

.redirect-info {
    background: #f8fafc;
    padding: 1rem;
    border-radius: var(--user-border-radius);
}

.redirect-info p {
    margin: 0.25rem 0;
    font-size: 0.875rem;
    color: var(--user-text);
}

.contact-card .contact-icon {
    font-size: 1.25rem;
}

.no-contacts {
    text-align: center;
    padding: 2rem;
    color: var(--user-text-secondary);
}

/* ===== FORM ELEMENTS ===== */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--user-text-dark);
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: var(--user-border-radius);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--user-primary);
    box-shadow: 0 0 0 3px rgba(255, 105, 0, 0.1);
}

.form-text {
    font-size: 0.875rem;
    color: var(--user-text-secondary);
    margin-top: 0.25rem;
}

/* ===== FORM ACTIONS ===== */
.form-actions {
    text-align: center;
    padding-top: 1rem;
    border-top: 2px solid #e5e7eb;
}

.user-btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    font-weight: 600;
}

/* ===== DEPOSIT HISTORY ===== */
.deposit-history-card {
    margin-bottom: 2rem;
}

.deposit-table {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.deposit-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: #f8fafc;
    border-radius: var(--user-border-radius);
    border: 1px solid #e2e8f0;
}

.deposit-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.deposit-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.deposit-details {
    flex: 1;
}

.deposit-amount {
    font-size: 1.125rem;
    font-weight: 700;
    color: #10b981;
    margin-bottom: 0.25rem;
}

.deposit-date {
    font-size: 0.875rem;
    color: var(--user-text-secondary);
    margin-bottom: 0.25rem;
}

.deposit-txn-id {
    font-size: 0.75rem;
    color: var(--user-text-secondary);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-weight: 600;
    margin-top: 2px;
}

.deposit-method {
    font-size: 0.75rem;
    color: var(--user-text-muted);
    font-style: italic;
    margin-top: 2px;
}

.deposit-status {
    text-align: right;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending { background: #fef3c7; color: #92400e; }
.status-completed { background: #d1fae5; color: #065f46; }
.status-failed { background: #fee2e2; color: #991b1b; }

/* ===== NO DATA STATES ===== */
.no-deposits {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--user-text-secondary);
}

.no-data-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-deposits h6 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--user-text-dark);
}

/* ===== FORM VALIDATION ===== */
.form-control.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.field-error {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    font-weight: 500;
}

/* ===== ICONS ===== */
.icon-upload::before { content: "📤"; }
.icon-deposit::before { content: "💰"; }
.icon-loading::before { content: "⏳"; }

/* Header Navigation Icons */
.icon-dashboard::before { content: "📊"; }
.icon-tasks::before { content: "📋"; }
.icon-list::before { content: "📄"; }
.icon-user::before { content: "👤"; }
.icon-chevron-down::before { content: "▼"; }
.icon-edit::before { content: "✏️"; }
.icon-notifications::before { content: "🔔"; }
.icon-salary::before { content: "💼"; }
.icon-withdraw::before { content: "💸"; }
.icon-records::before { content: "📊"; }
.icon-contact::before { content: "📞"; }
.icon-bank::before { content: "🏦"; }
.icon-logout::before { content: "🚪"; }

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .balance-banner {
        grid-template-columns: 1fr;
    }
    
    .contact-selection {
        grid-template-columns: 1fr;
    }
    
    .deposit-row {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .deposit-info {
        flex-direction: column;
        text-align: center;
    }
    
    .step-header {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}

/* ===== CLICKABLE DEPOSITS ===== */
.clickable-deposit {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.clickable-deposit:hover {
    background: rgba(255, 105, 0, 0.05) !important;
    border-color: rgba(255, 105, 0, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 105, 0, 0.15);
}

.clickable-deposit:active {
    transform: translateY(0);
}

.contact-hint {
    color: var(--user-primary);
    font-weight: 600;
    font-style: normal;
    margin-left: 0.5rem;
    font-size: 0.7rem;
}

.contact-action {
    margin-top: 0.5rem;
    color: var(--user-primary);
    font-size: 0.875rem;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.clickable-deposit:hover .contact-action {
    opacity: 1;
}

.icon-external-link::before {
    content: "🔗";
}

/* ===== NON-CLICKABLE DEPOSITS ===== */
.deposit-row:not(.clickable-deposit) {
    opacity: 0.8;
}

.deposit-row:not(.clickable-deposit):hover {
    background: #f8fafc !important;
    transform: none !important;
    box-shadow: none !important;
    cursor: default !important;
}
